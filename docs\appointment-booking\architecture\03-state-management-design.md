# State Management Design

## Overview

This document defines the state management architecture for the CRM appointment booking module, detailing Zustand store design, integration patterns with existing CRM stores, and data flow strategies.

## Architecture Principles

### 1. Store Composition Strategy
- **Modular Stores**: Separate stores for different concerns
- **CRM Integration**: Seamless integration with existing Zustand stores
- **Single Source of Truth**: Centralized state for booking flow
- **Performance Optimization**: Efficient selectors and updates

### 2. State Consistency
- **Immutable Updates**: All state updates create new objects
- **Predictable Flow**: Clear state transition rules
- **Error Recovery**: Robust error state management
- **Persistence**: Smart local storage integration

## Store Architecture

### 1. Booking Flow Store

**Purpose**: Manage the core booking flow state and progression

```typescript
interface BookingFlowStore {
  // Flow state
  currentStep: BookingFlowStep;
  progress: BookingProgress;
  isComplete: boolean;
  
  // Selection state
  selectedEventType: EventType | null;
  selectedDate: string | null;
  selectedTime: string | null;
  selectedTimezone: string;
  
  // Form state
  attendeeDetails: AttendeeDetails;
  bookingNotes: string;
  customFields: Record<string, any>;
  
  // Booking context
  currentBooking: Booking | null;
  isRescheduling: boolean;
  originalBooking: Booking | null;
  
  // UI state
  isLoading: boolean;
  error: BookingError | null;
  
  // Actions
  setCurrentStep: (step: BookingFlowStep) => void;
  selectEventType: (eventType: EventType) => void;
  selectDateTime: (date: string, time: string) => void;
  updateAttendeeDetails: (details: Partial<AttendeeDetails>) => void;
  setBookingNotes: (notes: string) => void;
  updateCustomField: (field: string, value: any) => void;
  createBooking: () => Promise<Booking>;
  resetFlow: () => void;
  
  // Navigation
  goToNextStep: () => void;
  goToPreviousStep: () => void;
  canProgress: () => boolean;
  
  // Error handling
  setError: (error: BookingError | null) => void;
  clearError: () => void;
}

enum BookingFlowStep {
  SELECTING_EVENT_TYPE = 'selecting_event_type',
  SELECTING_DATE = 'selecting_date',
  SELECTING_TIME = 'selecting_time',
  FILLING_DETAILS = 'filling_details',
  CONFIRMING = 'confirming',
  SUCCESS = 'success',
  ERROR = 'error'
}

interface BookingProgress {
  currentStep: number;
  totalSteps: number;
  completedSteps: boolean[];
  percentComplete: number;
}
```

**Implementation**:
```typescript
export const useBookingFlowStore = create<BookingFlowStore>((set, get) => ({
  // Initial state
  currentStep: BookingFlowStep.SELECTING_EVENT_TYPE,
  progress: {
    currentStep: 1,
    totalSteps: 5,
    completedSteps: [false, false, false, false, false],
    percentComplete: 0
  },
  isComplete: false,
  selectedEventType: null,
  selectedDate: null,
  selectedTime: null,
  selectedTimezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
  attendeeDetails: {
    name: '',
    email: '',
    phone: '',
    company: ''
  },
  bookingNotes: '',
  customFields: {},
  currentBooking: null,
  isRescheduling: false,
  originalBooking: null,
  isLoading: false,
  error: null,
  
  // Actions
  setCurrentStep: (step) => {
    const stepIndex = Object.values(BookingFlowStep).indexOf(step);
    const progress = get().progress;
    
    set({
      currentStep: step,
      progress: {
        ...progress,
        currentStep: stepIndex + 1,
        percentComplete: Math.round(((stepIndex + 1) / progress.totalSteps) * 100)
      }
    });
  },
  
  selectEventType: (eventType) => {
    set({ selectedEventType: eventType });
    get().goToNextStep();
  },
  
  selectDateTime: (date, time) => {
    set({ 
      selectedDate: date, 
      selectedTime: time 
    });
    get().goToNextStep();
  },
  
  updateAttendeeDetails: (details) => {
    set({
      attendeeDetails: {
        ...get().attendeeDetails,
        ...details
      }
    });
  },
  
  setBookingNotes: (notes) => set({ bookingNotes: notes }),
  
  updateCustomField: (field, value) => {
    set({
      customFields: {
        ...get().customFields,
        [field]: value
      }
    });
  },
  
  createBooking: async () => {
    const state = get();
    set({ isLoading: true, error: null });
    
    try {
      const bookingData = {
        eventTypeId: state.selectedEventType!.id,
        startTime: new Date(`${state.selectedDate}T${state.selectedTime}`),
        timezone: state.selectedTimezone,
        attendeeDetails: state.attendeeDetails,
        notes: state.bookingNotes,
        customFields: state.customFields
      };
      
      const booking = await BookingService.createBooking(bookingData);
      
      set({ 
        currentBooking: booking,
        isLoading: false,
        currentStep: BookingFlowStep.SUCCESS,
        isComplete: true
      });
      
      // Sync with CRM
      await get().syncWithCRM(booking);
      
      return booking;
    } catch (error) {
      set({ 
        isLoading: false, 
        error: error as BookingError,
        currentStep: BookingFlowStep.ERROR
      });
      throw error;
    }
  },
  
  goToNextStep: () => {
    const currentStep = get().currentStep;
    const canProgress = get().canProgress();
    
    if (!canProgress) return;
    
    const steps = Object.values(BookingFlowStep);
    const currentIndex = steps.indexOf(currentStep);
    const nextStep = steps[currentIndex + 1];
    
    if (nextStep) {
      get().setCurrentStep(nextStep);
    }
  },
  
  goToPreviousStep: () => {
    const currentStep = get().currentStep;
    const steps = Object.values(BookingFlowStep);
    const currentIndex = steps.indexOf(currentStep);
    const previousStep = steps[currentIndex - 1];
    
    if (previousStep) {
      get().setCurrentStep(previousStep);
    }
  },
  
  canProgress: () => {
    const state = get();
    
    switch (state.currentStep) {
      case BookingFlowStep.SELECTING_EVENT_TYPE:
        return !!state.selectedEventType;
      case BookingFlowStep.SELECTING_DATE:
        return !!state.selectedDate;
      case BookingFlowStep.SELECTING_TIME:
        return !!state.selectedTime;
      case BookingFlowStep.FILLING_DETAILS:
        return isValidAttendeeDetails(state.attendeeDetails);
      case BookingFlowStep.CONFIRMING:
        return true;
      default:
        return false;
    }
  },
  
  resetFlow: () => {
    set({
      currentStep: BookingFlowStep.SELECTING_EVENT_TYPE,
      progress: {
        currentStep: 1,
        totalSteps: 5,
        completedSteps: [false, false, false, false, false],
        percentComplete: 0
      },
      isComplete: false,
      selectedEventType: null,
      selectedDate: null,
      selectedTime: null,
      attendeeDetails: { name: '', email: '', phone: '', company: '' },
      bookingNotes: '',
      customFields: {},
      currentBooking: null,
      isRescheduling: false,
      originalBooking: null,
      error: null
    });
  },
  
  setError: (error) => set({ error }),
  clearError: () => set({ error: null }),
  
  // CRM Integration
  syncWithCRM: async (booking: Booking) => {
    const { addTask } = useTaskStore.getState();
    const { updateContact } = useContactStore.getState();
    
    // Create follow-up task
    addTask({
      id: generateId(),
      title: `Follow up: ${booking.attendeeName}`,
      description: `Follow up on appointment scheduled for ${formatDateTime(booking.startTime)}`,
      dueDate: addDays(booking.startTime, 1),
      contactId: booking.contactId,
      type: 'follow_up',
      status: 'pending'
    });
    
    // Update contact
    if (booking.contactId) {
      updateContact(booking.contactId, {
        lastAppointment: booking.startTime,
        totalAppointments: (contact.totalAppointments || 0) + 1
      });
    }
  }
}));
```

### 2. Event Types Store

**Purpose**: Manage event types and their configurations

```typescript
interface EventTypesStore {
  // Data
  eventTypes: EventType[];
  selectedEventType: EventType | null;
  
  // Filters and search
  searchQuery: string;
  filters: EventTypeFilters;
  sortBy: EventTypeSortOption;
  
  // State
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchEventTypes: () => Promise<void>;
  createEventType: (eventType: CreateEventTypeData) => Promise<EventType>;
  updateEventType: (id: string, updates: Partial<EventType>) => Promise<void>;
  deleteEventType: (id: string) => Promise<void>;
  selectEventType: (eventType: EventType) => void;
  
  // Filtering and search
  setSearchQuery: (query: string) => void;
  setFilters: (filters: Partial<EventTypeFilters>) => void;
  setSortBy: (sort: EventTypeSortOption) => void;
  getFilteredEventTypes: () => EventType[];
  
  // Bulk operations
  bulkUpdateEventTypes: (ids: string[], updates: Partial<EventType>) => Promise<void>;
  bulkDeleteEventTypes: (ids: string[]) => Promise<void>;
}

interface EventTypeFilters {
  duration?: number[];
  isActive?: boolean;
  requiresConfirmation?: boolean;
  tags?: string[];
}

enum EventTypeSortOption {
  NAME_ASC = 'name_asc',
  NAME_DESC = 'name_desc',
  DURATION_ASC = 'duration_asc',
  DURATION_DESC = 'duration_desc',
  CREATED_ASC = 'created_asc',
  CREATED_DESC = 'created_desc'
}
```

### 3. Availability Store

**Purpose**: Manage availability schedules and time slot calculations

```typescript
interface AvailabilityStore {
  // Availability data
  schedules: AvailabilitySchedule[];
  currentSchedule: AvailabilitySchedule | null;
  
  // Time slots
  availableSlots: Record<string, TimeSlot[]>; // date -> slots
  slotsCache: Map<string, { data: TimeSlot[]; timestamp: number }>;
  
  // Calendar state
  currentMonth: Date;
  selectedDate: string | null;
  timezone: string;
  
  // State
  isLoadingSlots: boolean;
  isLoadingSchedules: boolean;
  error: string | null;
  
  // Actions
  fetchSchedules: () => Promise<void>;
  fetchAvailableSlots: (eventTypeId: string, date: string) => Promise<TimeSlot[]>;
  createSchedule: (schedule: CreateScheduleData) => Promise<AvailabilitySchedule>;
  updateSchedule: (id: string, updates: Partial<AvailabilitySchedule>) => Promise<void>;
  
  // Slot management
  getAvailableSlots: (eventTypeId: string, date: string) => TimeSlot[];
  reserveSlot: (slot: TimeSlot) => Promise<void>;
  releaseSlot: (slot: TimeSlot) => Promise<void>;
  
  // Calendar navigation
  setCurrentMonth: (month: Date) => void;
  setSelectedDate: (date: string) => void;
  setTimezone: (timezone: string) => void;
  
  // Cache management
  invalidateSlots: (date?: string) => void;
  clearCache: () => void;
  
  // Bulk operations
  fetchMultipleDays: (eventTypeId: string, dates: string[]) => Promise<void>;
  prefetchUpcomingDays: (eventTypeId: string, days: number) => Promise<void>;
}
```

### 4. Bookings Management Store

**Purpose**: Manage existing bookings and appointment history

```typescript
interface BookingsStore {
  // Data
  bookings: Booking[];
  upcomingBookings: Booking[];
  pastBookings: Booking[];
  
  // Filters
  filters: BookingFilters;
  searchQuery: string;
  sortBy: BookingSortOption;
  
  // Pagination
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    hasMore: boolean;
  };
  
  // State
  isLoading: boolean;
  error: string | null;
  
  // Actions
  fetchBookings: (params?: FetchBookingsParams) => Promise<void>;
  fetchBookingById: (id: string) => Promise<Booking>;
  updateBooking: (id: string, updates: Partial<Booking>) => Promise<void>;
  cancelBooking: (id: string, reason?: string) => Promise<void>;
  rescheduleBooking: (id: string, newDateTime: Date) => Promise<void>;
  
  // Filtering and search
  setFilters: (filters: Partial<BookingFilters>) => void;
  setSearchQuery: (query: string) => void;
  setSortBy: (sort: BookingSortOption) => void;
  getFilteredBookings: () => Booking[];
  
  // Bulk operations
  bulkCancelBookings: (ids: string[], reason?: string) => Promise<void>;
  bulkRescheduleBookings: (updates: Array<{ id: string; newDateTime: Date }>) => Promise<void>;
  
  // Statistics
  getBookingStats: () => BookingStats;
  getUpcomingBookings: (days?: number) => Booking[];
  getPastBookings: (days?: number) => Booking[];
}

interface BookingFilters {
  status?: BookingStatus[];
  eventTypeId?: string[];
  dateRange?: { start: Date; end: Date };
  contactId?: string;
}

enum BookingSortOption {
  DATE_ASC = 'date_asc',
  DATE_DESC = 'date_desc',
  CREATED_ASC = 'created_asc',
  CREATED_DESC = 'created_desc',
  STATUS = 'status'
}

interface BookingStats {
  total: number;
  upcoming: number;
  past: number;
  cancelled: number;
  noShow: number;
  completed: number;
  byStatus: Record<BookingStatus, number>;
  byEventType: Record<string, number>;
}
```

## Store Integration Patterns

### 1. CRM Store Integration

**Contact Store Integration**:
```typescript
// Enhanced contact store with appointment data
interface EnhancedContactStore extends ContactStore {
  // Appointment-specific data
  getContactBookings: (contactId: string) => Booking[];
  getContactUpcomingBookings: (contactId: string) => Booking[];
  getContactBookingHistory: (contactId: string) => Booking[];
  
  // Actions
  linkBookingToContact: (bookingId: string, contactId: string) => void;
  updateContactFromBooking: (contactId: string, booking: Booking) => void;
  
  // Statistics
  getContactBookingStats: (contactId: string) => ContactBookingStats;
}

// Integration hook
export const useContactBookingIntegration = () => {
  const bookings = useBookingsStore(state => state.bookings);
  const { updateContact } = useContactStore();
  
  const linkBookingToContact = useCallback((booking: Booking, contactId: string) => {
    // Update booking with contact reference
    useBookingsStore.getState().updateBooking(booking.id, { contactId });
    
    // Update contact with appointment data
    updateContact(contactId, {
      lastAppointment: booking.startTime,
      totalAppointments: (contact.totalAppointments || 0) + 1
    });
  }, [updateContact]);
  
  const getContactBookings = useCallback((contactId: string) => {
    return bookings.filter(booking => booking.contactId === contactId);
  }, [bookings]);
  
  return {
    linkBookingToContact,
    getContactBookings
  };
};
```

**Task Store Integration**:
```typescript
// Task automation for appointments
export const useAppointmentTaskIntegration = () => {
  const { addTask, updateTask } = useTaskStore();
  
  const createAppointmentTasks = useCallback(async (booking: Booking) => {
    // Pre-appointment preparation task
    addTask({
      id: generateId(),
      title: `Prepare for: ${booking.attendeeName}`,
      description: `Prepare for appointment on ${formatDateTime(booking.startTime)}`,
      dueDate: subHours(booking.startTime, 1),
      contactId: booking.contactId,
      type: 'preparation',
      status: 'pending',
      metadata: {
        bookingId: booking.id,
        eventType: booking.eventType.title
      }
    });
    
    // Follow-up task
    addTask({
      id: generateId(),
      title: `Follow up: ${booking.attendeeName}`,
      description: `Follow up on appointment held on ${formatDateTime(booking.startTime)}`,
      dueDate: addDays(booking.startTime, 1),
      contactId: booking.contactId,
      type: 'follow_up',
      status: 'pending',
      metadata: {
        bookingId: booking.id,
        eventType: booking.eventType.title
      }
    });
  }, [addTask]);
  
  const updateTasksOnBookingChange = useCallback((booking: Booking, changes: Partial<Booking>) => {
    // Update related tasks when booking is modified
    const tasks = useTaskStore.getState().tasks.filter(
      task => task.metadata?.bookingId === booking.id
    );
    
    tasks.forEach(task => {
      if (changes.startTime) {
        const newDueDate = task.type === 'preparation' 
          ? subHours(changes.startTime, 1)
          : addDays(changes.startTime, 1);
          
        updateTask(task.id, { 
          dueDate: newDueDate,
          description: task.description.replace(
            formatDateTime(booking.startTime),
            formatDateTime(changes.startTime)
          )
        });
      }
    });
  }, [updateTask]);
  
  return {
    createAppointmentTasks,
    updateTasksOnBookingChange
  };
};
```

### 2. Data Persistence Strategy

**Local Storage Integration**:
```typescript
// Persistent store middleware
const createPersistentStore = <T extends object>(
  name: string,
  store: StateCreator<T>
) => {
  return create<T>(
    persist(
      store,
      {
        name: `appointment-${name}`,
        getStorage: () => localStorage,
        partialize: (state) => {
          // Only persist specific parts of state
          const { isLoading, error, ...persistedState } = state;
          return persistedState;
        },
        merge: (persistedState, currentState) => {
          // Smart merge strategy
          return {
            ...currentState,
            ...persistedState,
            // Reset transient state
            isLoading: false,
            error: null
          };
        }
      }
    )
  );
};

// Usage
export const useBookingFlowStore = createPersistentStore(
  'booking-flow',
  (set, get) => ({
    // Store implementation
  })
);
```

**Cache Management**:
```typescript
// Smart caching for availability data
interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number; // time to live in milliseconds
}

class AvailabilityCache {
  private cache = new Map<string, CacheEntry<TimeSlot[]>>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes
  
  set(key: string, data: TimeSlot[], ttl = this.DEFAULT_TTL) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }
  
  get(key: string): TimeSlot[] | null {
    const entry = this.cache.get(key);
    
    if (!entry) return null;
    
    const isExpired = Date.now() - entry.timestamp > entry.ttl;
    if (isExpired) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }
  
  invalidate(pattern?: string) {
    if (!pattern) {
      this.cache.clear();
      return;
    }
    
    const keysToDelete = Array.from(this.cache.keys())
      .filter(key => key.includes(pattern));
    
    keysToDelete.forEach(key => this.cache.delete(key));
  }
  
  // Background cleanup of expired entries
  cleanup() {
    const now = Date.now();
    
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

// Integration with availability store
const availabilityCache = new AvailabilityCache();

// Use in store
fetchAvailableSlots: async (eventTypeId: string, date: string) => {
  const cacheKey = `${eventTypeId}:${date}`;
  
  // Check cache first
  const cachedSlots = availabilityCache.get(cacheKey);
  if (cachedSlots) {
    return cachedSlots;
  }
  
  // Fetch from API
  set({ isLoadingSlots: true });
  try {
    const slots = await AvailabilityService.getSlots(eventTypeId, date);
    
    // Cache the result
    availabilityCache.set(cacheKey, slots);
    
    set({ 
      availableSlots: {
        ...get().availableSlots,
        [date]: slots
      },
      isLoadingSlots: false
    });
    
    return slots;
  } catch (error) {
    set({ isLoadingSlots: false, error: error.message });
    throw error;
  }
}
```

## State Synchronization

### 1. Real-time Updates

**WebSocket Integration**:
```typescript
// Real-time availability updates
export const useRealTimeAvailability = (eventTypeId: string) => {
  const { invalidateSlots, fetchAvailableSlots } = useAvailabilityStore();
  
  useEffect(() => {
    const ws = new WebSocket(`${WS_URL}/availability/${eventTypeId}`);
    
    ws.onmessage = (event) => {
      const update = JSON.parse(event.data);
      
      switch (update.type) {
        case 'SLOT_BOOKED':
          // Invalidate cache for the affected date
          invalidateSlots(update.date);
          // Optionally refetch
          fetchAvailableSlots(eventTypeId, update.date);
          break;
          
        case 'SLOT_CANCELLED':
          // Refresh availability
          invalidateSlots(update.date);
          fetchAvailableSlots(eventTypeId, update.date);
          break;
          
        case 'SCHEDULE_UPDATED':
          // Clear all cache for this event type
          invalidateSlots();
          break;
      }
    };
    
    return () => ws.close();
  }, [eventTypeId]);
};
```

### 2. Optimistic Updates

**Booking Creation with Optimistic Updates**:
```typescript
// Optimistic booking creation
const createBookingOptimistic = async (bookingData: CreateBookingData) => {
  const tempBooking: Booking = {
    id: `temp-${Date.now()}`,
    ...bookingData,
    status: BookingStatus.PENDING,
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  // Optimistic update - add booking immediately
  set(state => ({
    bookings: [...state.bookings, tempBooking],
    currentBooking: tempBooking
  }));
  
  try {
    // Make API call
    const createdBooking = await BookingService.createBooking(bookingData);
    
    // Replace temp booking with real booking
    set(state => ({
      bookings: state.bookings.map(booking => 
        booking.id === tempBooking.id ? createdBooking : booking
      ),
      currentBooking: createdBooking
    }));
    
    return createdBooking;
  } catch (error) {
    // Rollback optimistic update
    set(state => ({
      bookings: state.bookings.filter(booking => booking.id !== tempBooking.id),
      currentBooking: null,
      error: error.message
    }));
    
    throw error;
  }
};
```

## Performance Optimization

### 1. Selector Optimization

**Efficient Selectors**:
```typescript
// Memoized selectors for performance
export const useBookingSelectors = () => {
  // Only re-render when specific data changes
  const upcomingBookings = useBookingsStore(
    state => state.bookings.filter(booking => 
      booking.startTime > new Date() && 
      booking.status !== BookingStatus.CANCELLED
    ),
    shallow
  );
  
  const bookingsByStatus = useBookingsStore(
    state => state.bookings.reduce((acc, booking) => {
      acc[booking.status] = (acc[booking.status] || 0) + 1;
      return acc;
    }, {} as Record<BookingStatus, number>),
    shallow
  );
  
  // Derived data with memoization
  const bookingStats = useMemo(() => ({
    total: upcomingBookings.length,
    thisWeek: upcomingBookings.filter(booking => 
      isThisWeek(booking.startTime)
    ).length,
    thisMonth: upcomingBookings.filter(booking => 
      isThisMonth(booking.startTime)
    ).length
  }), [upcomingBookings]);
  
  return {
    upcomingBookings,
    bookingsByStatus,
    bookingStats
  };
};
```

### 2. Batch Updates

**Batch State Updates**:
```typescript
// Batch multiple updates into single state change
const updateBookingFlow = (updates: Partial<BookingFlowState>) => {
  set(state => ({
    ...state,
    ...updates,
    // Update progress if step changed
    ...(updates.currentStep && {
      progress: calculateProgress(updates.currentStep, state.progress.totalSteps)
    })
  }));
};

// Batch API calls
const batchUpdateBookings = async (updates: Array<{ id: string; data: Partial<Booking> }>) => {
  set({ isLoading: true });
  
  try {
    // Batch API call
    const results = await BookingService.batchUpdate(updates);
    
    // Single state update
    set(state => ({
      bookings: state.bookings.map(booking => {
        const update = results.find(result => result.id === booking.id);
        return update ? { ...booking, ...update.data } : booking;
      }),
      isLoading: false
    }));
  } catch (error) {
    set({ isLoading: false, error: error.message });
  }
};
```

## Testing Strategy

### Store Testing Patterns

```typescript
// Store testing utilities
export const createTestStore = <T>(initialState?: Partial<T>) => {
  return create<T>(() => ({
    ...defaultState,
    ...initialState
  }));
};

// Store integration tests
describe('BookingFlowStore', () => {
  let store: BookingFlowStore;
  
  beforeEach(() => {
    store = createTestStore<BookingFlowStore>();
  });
  
  it('should progress through booking flow', () => {
    const { selectEventType, goToNextStep, currentStep } = store;
    
    // Select event type
    selectEventType(mockEventType);
    expect(currentStep).toBe(BookingFlowStep.SELECTING_DATE);
    
    // Progress to next step
    store.selectedDate = '2024-03-15';
    goToNextStep();
    expect(currentStep).toBe(BookingFlowStep.SELECTING_TIME);
  });
  
  it('should handle booking creation', async () => {
    const mockBooking = createMockBooking();
    jest.spyOn(BookingService, 'createBooking').mockResolvedValue(mockBooking);
    
    // Set up booking data
    store.selectedEventType = mockEventType;
    store.selectedDate = '2024-03-15';
    store.selectedTime = '09:00';
    store.attendeeDetails = mockAttendeeDetails;
    
    // Create booking
    const result = await store.createBooking();
    
    expect(result).toEqual(mockBooking);
    expect(store.currentBooking).toEqual(mockBooking);
    expect(store.currentStep).toBe(BookingFlowStep.SUCCESS);
  });
  
  it('should handle errors gracefully', async () => {
    const error = new Error('Booking failed');
    jest.spyOn(BookingService, 'createBooking').mockRejectedValue(error);
    
    try {
      await store.createBooking();
    } catch (e) {
      expect(store.error).toEqual(error);
      expect(store.currentStep).toBe(BookingFlowStep.ERROR);
    }
  });
});

// Integration tests with multiple stores
describe('Store Integration', () => {
  it('should sync booking with CRM stores', async () => {
    const booking = await createBooking(mockBookingData);
    
    // Check task creation
    const tasks = useTaskStore.getState().tasks;
    const bookingTasks = tasks.filter(task => 
      task.metadata?.bookingId === booking.id
    );
    expect(bookingTasks).toHaveLength(2); // prep + follow-up
    
    // Check contact update
    if (booking.contactId) {
      const contact = useContactStore.getState().contacts.find(
        c => c.id === booking.contactId
      );
      expect(contact?.lastAppointment).toEqual(booking.startTime);
    }
  });
});
```

## Migration Strategy

### 1. Gradual Migration
- Start with new booking flow store
- Integrate with existing contact/task stores
- Gradually add new features
- Maintain backward compatibility

### 2. Data Migration
- Import existing appointment data
- Map to new data structures
- Preserve historical data
- Validate data integrity

### 3. Store Evolution
- Version store schemas
- Handle breaking changes gracefully
- Provide migration utilities
- Monitor performance impact

## Related Documents

- [01-crm-integration-design.md](./01-crm-integration-design.md) - Overall architecture
- [02-component-architecture.md](./02-component-architecture.md) - Component design
- [04-ui-component-design.md](./04-ui-component-design.md) - UI specifications
- [../analysis/03-state-management.md](../analysis/03-state-management.md) - Cal.com state analysis
