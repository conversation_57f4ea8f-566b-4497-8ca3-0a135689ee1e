# Integration Points & File Structure - Appointment Booking Module

## Table of Contents
1. [File Structure Overview](#file-structure-overview)
2. [Integration with Existing CRM](#integration-with-existing-crm)
3. [Shared Components Usage](#shared-components-usage)
4. [Service Layer Integration](#service-layer-integration)
5. [Navigation & Routing](#navigation--routing)
6. [Data Flow Integration](#data-flow-integration)

## File Structure Overview

The appointment booking module integrates seamlessly with the existing CRM structure, following established patterns and conventions.

### Complete File Structure
```
src/app/(main)/appointments/
├── components/
│   ├── booking/
│   │   ├── booker/
│   │   │   ├── Booker.tsx
│   │   │   ├── BookerHeader.tsx
│   │   │   ├── BookerSection.tsx
│   │   │   └── index.ts
│   │   ├── event-form/
│   │   │   ├── BookEventForm.tsx
│   │   │   ├── BookingFields.tsx
│   │   │   ├── GuestManagement.tsx
│   │   │   └── index.ts
│   │   ├── calendar/
│   │   │   ├── DatePicker.tsx
│   │   │   ├── CalendarDay.tsx
│   │   │   ├── CalendarGrid.tsx
│   │   │   ├── CalendarNavigation.tsx
│   │   │   └── index.ts
│   │   ├── time-slots/
│   │   │   ├── AvailableTimeSlots.tsx
│   │   │   ├── TimeSlotButton.tsx
│   │   │   ├── TimeSlotGrid.tsx
│   │   │   └── index.ts
│   │   └── confirmation/
│   │       ├── BookingConfirmation.tsx
│   │       ├── CalendarLinks.tsx
│   │       ├── BookingDetails.tsx
│   │       └── index.ts
│   ├── event-types/
│   │   ├── create/
│   │   │   ├── CreateEventTypeDialog.tsx
│   │   │   ├── CreateEventTypeForm.tsx
│   │   │   ├── EventTypeWizard.tsx
│   │   │   └── index.ts
│   │   ├── edit/
│   │   │   ├── EditEventType.tsx
│   │   │   ├── EventTypeSettings.tsx
│   │   │   ├── EventTypeTabs.tsx
│   │   │   └── index.ts
│   │   ├── list/
│   │   │   ├── EventTypesList.tsx
│   │   │   ├── EventTypeCard.tsx
│   │   │   ├── EventTypeFilters.tsx
│   │   │   └── index.ts
│   │   └── shared/
│   │       ├── EventTypeForm.tsx
│   │       ├── LocationSettings.tsx
│   │       ├── BookingFieldsEditor.tsx
│   │       └── index.ts
│   ├── availability/
│   │   ├── schedule/
│   │   │   ├── ScheduleEditor.tsx
│   │   │   ├── DayAvailability.tsx
│   │   │   ├── TimeRangeEditor.tsx
│   │   │   └── index.ts
│   │   ├── overrides/
│   │   │   ├── DateOverrides.tsx
│   │   │   ├── OverrideCalendar.tsx
│   │   │   ├── OverrideForm.tsx
│   │   │   └── index.ts
│   │   └── timezone/
│   │       ├── TimezoneSelector.tsx
│   │       ├── TimezoneConverter.tsx
│   │       └── index.ts
│   ├── views/
│   │   ├── calendar-view/
│   │   │   ├── FullCalendarView.tsx
│   │   │   ├── WeeklyCalendarView.tsx
│   │   │   ├── MonthlyCalendarView.tsx
│   │   │   └── index.ts
│   │   ├── list-view/
│   │   │   ├── BookingListView.tsx
│   │   │   ├── BookingListItem.tsx
│   │   │   ├── BookingFilters.tsx
│   │   │   └── index.ts
│   │   └── grid-view/
│   │       ├── BookingGridView.tsx
│   │       ├── BookingGridCard.tsx
│   │       └── index.ts
│   └── shared/
│       ├── BookingCard.tsx
│       ├── StatusBadge.tsx
│       ├── AvailabilityIndicator.tsx
│       ├── LoadingStates.tsx
│       └── index.ts
├── hooks/
│   ├── use-booking-store.ts
│   ├── use-event-types.ts
│   ├── use-availability.ts
│   ├── use-booking-form.ts
│   ├── use-calendar-navigation.ts
│   └── index.ts
├── lib/
│   ├── types/
│   │   ├── booking.ts
│   │   ├── event-type.ts
│   │   ├── availability.ts
│   │   ├── calendar.ts
│   │   └── index.ts
│   ├── utils/
│   │   ├── date-utils.ts
│   │   ├── time-utils.ts
│   │   ├── booking-utils.ts
│   │   ├── validation-utils.ts
│   │   └── index.ts
│   ├── services/
│   │   ├── booking-service.ts
│   │   ├── event-type-service.ts
│   │   ├── availability-service.ts
│   │   ├── calendar-service.ts
│   │   └── index.ts
│   └── stores/
│       ├── booking-store.ts
│       ├── event-type-store.ts
│       ├── availability-store.ts
│       ├── ui-store.ts
│       └── index.ts
├── data/
│   ├── mock-bookings.ts
│   ├── mock-event-types.ts
│   ├── mock-availability.ts
│   ├── mock-users.ts
│   └── index.ts
├── [username]/
│   ├── [event-slug]/
│   │   ├── page.tsx
│   │   ├── components/
│   │   │   ├── PublicBooker.tsx
│   │   │   ├── PublicEventInfo.tsx
│   │   │   └── index.ts
│   │   └── loading.tsx
│   ├── page.tsx
│   └── components/
│       ├── UserProfile.tsx
│       ├── EventTypeList.tsx
│       └── index.ts
├── event-types/
│   ├── page.tsx
│   ├── [id]/
│   │   ├── page.tsx
│   │   ├── edit/
│   │   │   └── page.tsx
│   │   └── settings/
│   │       └── page.tsx
│   └── new/
│       └── page.tsx
├── bookings/
│   ├── page.tsx
│   ├── [id]/
│   │   ├── page.tsx
│   │   ├── reschedule/
│   │   │   └── page.tsx
│   │   └── cancel/
│   │       └── page.tsx
│   └── components/
│       ├── BookingsHeader.tsx
│       ├── BookingsTable.tsx
│       └── index.ts
├── availability/
│   ├── page.tsx
│   ├── [schedule-id]/
│   │   └── page.tsx
│   └── components/
│       ├── AvailabilityHeader.tsx
│       ├── SchedulesList.tsx
│       └── index.ts
├── settings/
│   ├── page.tsx
│   └── components/
│       ├── AppointmentSettings.tsx
│       ├── NotificationSettings.tsx
│       └── index.ts
└── page.tsx
```

## Integration with Existing CRM

### Shared Component Usage
```typescript
// Import existing CRM components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Skeleton } from '@/components/ui/skeleton';

// Import existing layout components
import { PageHeader } from '@/components/page-header';
import { AppSidebar } from '@/components/layout/app-sidebar';
import { AppHeader } from '@/components/layout/app-header';

// Import existing data table components
import { DataTable } from '@/components/generic-data-table';
import { FilterBar } from '@/components/filters/filter-bar';
import { Pagination } from '@/components/generic-data-table/pagination';

// Import existing utility components
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { ErrorBoundary } from '@/components/error-boundaries';
import { EmptyState } from '@/components/generic-data-table/empty-state';
```

### Service Layer Integration
```typescript
// Extend existing service patterns
import { BaseService } from '@/lib/services';
import type { ApiResponse, PaginatedResponse } from '@/lib/types';

export class BookingService extends BaseService {
  constructor() {
    super('/api/appointments');
  }
  
  async getBookings(filters?: BookingFilters): Promise<PaginatedResponse<Booking>> {
    return this.get('/bookings', { params: filters });
  }
  
  async createBooking(data: CreateBookingData): Promise<ApiResponse<Booking>> {
    return this.post('/bookings', data);
  }
  
  async updateBooking(id: string, data: Partial<Booking>): Promise<ApiResponse<Booking>> {
    return this.put(`/bookings/${id}`, data);
  }
  
  async deleteBooking(id: string): Promise<ApiResponse<void>> {
    return this.delete(`/bookings/${id}`);
  }
}

// Use existing error handling patterns
import { handleApiError } from '@/lib/utils/error-utils';
import { showToast } from '@/lib/utils/toast';

export const useBookingMutations = () => {
  const createBooking = useMutation({
    mutationFn: (data: CreateBookingData) => bookingService.createBooking(data),
    onSuccess: (response) => {
      showToast('Booking created successfully', 'success');
      queryClient.invalidateQueries(['bookings']);
    },
    onError: (error) => {
      handleApiError(error);
    }
  });
  
  return { createBooking };
};
```

### Store Integration
```typescript
// Extend existing Zustand patterns
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// Follow existing store structure
export const useBookingStore = create<BookingStore>()(
  devtools(
    persist(
      immer((set, get) => ({
        // State
        bookings: [],
        selectedBooking: null,
        filters: DEFAULT_BOOKING_FILTERS,
        isLoading: false,
        error: null,
        
        // Actions following existing patterns
        setBookings: (bookings) => set((state) => {
          state.bookings = bookings;
        }),
        
        addBooking: (booking) => set((state) => {
          state.bookings.push(booking);
        }),
        
        updateBooking: (id, updates) => set((state) => {
          const index = state.bookings.findIndex(b => b.id === id);
          if (index !== -1) {
            Object.assign(state.bookings[index], updates);
          }
        }),
        
        // Async actions with error handling
        fetchBookings: async (filters) => {
          set((state) => {
            state.isLoading = true;
            state.error = null;
          });
          
          try {
            const response = await bookingService.getBookings(filters);
            set((state) => {
              state.bookings = response.data;
              state.isLoading = false;
            });
          } catch (error) {
            set((state) => {
              state.error = error.message;
              state.isLoading = false;
            });
          }
        }
      })),
      {
        name: 'booking-store',
        partialize: (state) => ({
          filters: state.filters,
          selectedBooking: state.selectedBooking
        })
      }
    ),
    { name: 'booking-store' }
  )
);
```

## Navigation & Routing

### Sidebar Integration
```typescript
// Add to existing sidebar data
import { Calendar, Clock, Settings, Users } from 'lucide-react';

export const appointmentNavItems = [
  {
    name: 'Appointments',
    href: '/appointments',
    icon: Calendar,
    current: false,
    children: [
      {
        name: 'Bookings',
        href: '/appointments/bookings',
        icon: Calendar,
        current: false
      },
      {
        name: 'Event Types',
        href: '/appointments/event-types',
        icon: Clock,
        current: false
      },
      {
        name: 'Availability',
        href: '/appointments/availability',
        icon: Users,
        current: false
      },
      {
        name: 'Settings',
        href: '/appointments/settings',
        icon: Settings,
        current: false
      }
    ]
  }
];

// Integrate with existing sidebar data
import { sidebarData } from '@/components/layout/sidebar-data';

export const extendedSidebarData = [
  ...sidebarData,
  ...appointmentNavItems
];
```

### Route Structure
```typescript
// Follow existing route patterns
const appointmentRoutes = {
  // Main appointments dashboard
  appointments: '/appointments',
  
  // Booking management
  bookings: '/appointments/bookings',
  bookingDetail: (id: string) => `/appointments/bookings/${id}`,
  rescheduleBooking: (id: string) => `/appointments/bookings/${id}/reschedule`,
  cancelBooking: (id: string) => `/appointments/bookings/${id}/cancel`,
  
  // Event type management
  eventTypes: '/appointments/event-types',
  createEventType: '/appointments/event-types/new',
  editEventType: (id: string) => `/appointments/event-types/${id}/edit`,
  eventTypeSettings: (id: string) => `/appointments/event-types/${id}/settings`,
  
  // Availability management
  availability: '/appointments/availability',
  editSchedule: (id: string) => `/appointments/availability/${id}`,
  
  // Public booking pages
  userBookingPage: (username: string) => `/${username}`,
  eventBookingPage: (username: string, eventSlug: string) => `/${username}/${eventSlug}`,
  
  // Settings
  appointmentSettings: '/appointments/settings'
};
```

## Data Flow Integration

### Type System Integration
```typescript
// Extend existing type definitions
import type { BaseEntity, TimestampedEntity } from '@/lib/types/shared';
import type { User } from '@/lib/types/contact';

export interface Booking extends BaseEntity, TimestampedEntity {
  // Booking-specific fields
  eventTypeId: string;
  userId: string;
  startTime: Date;
  endTime: Date;
  timezone: string;
  status: BookingStatus;
  
  // Relations (following existing patterns)
  eventType?: EventType;
  user?: User;
  attendees: Attendee[];
  
  // Metadata
  location: LocationInfo;
  notes?: string;
  confirmationNumber: string;
  payment?: PaymentInfo;
}

// Use existing validation patterns
import { z } from 'zod';

export const createBookingSchema = z.object({
  eventTypeId: z.string().uuid(),
  startTime: z.date(),
  endTime: z.date(),
  timezone: z.string(),
  attendeeInfo: z.object({
    name: z.string().min(1),
    email: z.string().email(),
    phone: z.string().optional(),
    notes: z.string().optional()
  }),
  customFields: z.record(z.any()).optional()
});
```

### API Integration
```typescript
// Follow existing API patterns
import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { validateRequest } from '@/lib/utils/validation';
import { handleApiError } from '@/lib/utils/error-utils';

export async function POST(request: NextRequest) {
  try {
    // Use existing auth patterns
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Use existing validation patterns
    const body = await request.json();
    const validatedData = validateRequest(createBookingSchema, body);
    
    // Business logic
    const booking = await bookingService.createBooking({
      ...validatedData,
      userId: session.user.id
    });
    
    return NextResponse.json({ data: booking }, { status: 201 });
    
  } catch (error) {
    return handleApiError(error);
  }
}
```

### Component Integration Patterns
```typescript
// Use existing component patterns
import { usePageMetadata } from '@/hooks/use-page-metadata';
import { usePageActions } from '@/hooks/use-page-actions';

export const BookingsPage = () => {
  // Follow existing page structure patterns
  usePageMetadata({
    title: 'Bookings',
    description: 'Manage your appointment bookings'
  });
  
  const pageActions = usePageActions([
    {
      label: 'New Booking',
      action: () => router.push('/appointments/bookings/new'),
      variant: 'primary'
    },
    {
      label: 'Export',
      action: () => exportBookings(),
      variant: 'secondary'
    }
  ]);
  
  return (
    <div className="appointments-page">
      <PageHeader
        title="Bookings"
        description="Manage your appointment bookings"
        actions={pageActions}
      />
      
      <div className="appointments-content">
        <BookingListView />
      </div>
    </div>
  );
};

// Use existing error boundary patterns
export default function BookingsPageWithErrorBoundary() {
  return (
    <ErrorBoundary fallback={<BookingsErrorFallback />}>
      <BookingsPage />
    </ErrorBoundary>
  );
}
```

### State Synchronization
```typescript
// Integrate with existing state management
import { useContactStore } from '@/lib/stores/contacts-store';
import { useTaskStore } from '@/lib/stores/tasks-store';

export const useBookingIntegration = () => {
  const { addContact } = useContactStore();
  const { createTask } = useTaskStore();
  
  const createBookingWithIntegration = async (bookingData: CreateBookingData) => {
    // Create the booking
    const booking = await bookingService.createBooking(bookingData);
    
    // Integrate with contacts
    const existingContact = await contactService.findByEmail(bookingData.attendeeInfo.email);
    if (!existingContact) {
      await addContact({
        name: bookingData.attendeeInfo.name,
        email: bookingData.attendeeInfo.email,
        phone: bookingData.attendeeInfo.phone,
        source: 'appointment_booking',
        tags: ['appointment_attendee']
      });
    }
    
    // Create follow-up task
    await createTask({
      title: `Follow up on ${booking.eventType.title} with ${bookingData.attendeeInfo.name}`,
      description: `Appointment scheduled for ${format(booking.startTime, 'PPP')}`,
      dueDate: addDays(booking.startTime, 1),
      priority: 'medium',
      category: 'follow_up',
      relatedEntityType: 'booking',
      relatedEntityId: booking.id
    });
    
    return booking;
  };
  
  return { createBookingWithIntegration };
};
```

This integration approach ensures the appointment booking module seamlessly extends the existing CRM while maintaining consistency in patterns, conventions, and user experience.
