# Mock Data Structures - Appointment Booking Module

## Table of Contents
1. [Mock Data Overview](#mock-data-overview)
2. [User Profile Data](#user-profile-data)
3. [Event Types Data](#event-types-data)
4. [Availability & Schedules](#availability--schedules)
5. [Bookings Data](#bookings-data)
6. [Data Generation Utilities](#data-generation-utilities)

## Mock Data Overview

The mock data system provides realistic, comprehensive sample data for a single user scenario with related events, bookings, and availability. This data supports development, testing, and demonstration of the appointment booking module.

### Data Relationships
```typescript
interface MockDataStructure {
  user: UserProfile;
  eventTypes: EventType[];
  schedules: Schedule[];
  bookings: Booking[];
  availability: AvailabilityData;
  
  // Relationships
  userEventTypes: string[]; // Event type IDs owned by user
  eventTypeBookings: Record<string, string[]>; // Event type ID -> Booking IDs
  scheduleEventTypes: Record<string, string[]>; // Schedule ID -> Event type IDs
}
```

### Mock Data Principles
- **Realistic Data**: Names, emails, and scenarios that feel authentic
- **Consistent Relationships**: All foreign keys reference valid entities
- **Temporal Logic**: Bookings and availability follow realistic time patterns
- **Variety**: Different event types, booking statuses, and scenarios
- **Scalable**: Easy to generate more data or modify existing data

## User Profile Data

### Primary User Profile
```typescript
const MOCK_USER: UserProfile = {
  id: 'user_001',
  name: 'Sarah Johnson',
  email: '<EMAIL>',
  username: 'sarah-johnson',
  avatar: '/avatars/sarah-johnson.jpg',
  
  // Profile settings
  timezone: 'America/New_York',
  locale: 'en-US',
  timeFormat: 12, // 12 or 24 hour
  weekStart: 1, // Monday = 1, Sunday = 0
  
  // Business information
  title: 'Senior Marketing Consultant',
  company: 'Digital Growth Solutions',
  bio: 'Helping businesses scale their digital presence through strategic marketing and data-driven insights. 10+ years of experience in B2B marketing.',
  
  // Contact information
  phone: '+****************',
  website: 'https://sarahjohnson.consulting',
  
  // Social links
  socialLinks: {
    linkedin: 'https://linkedin.com/in/sarah-johnson-marketing',
    twitter: 'https://twitter.com/sarahj_marketing',
    instagram: 'https://instagram.com/sarahj_consulting'
  },
  
  // Branding
  brandColor: '#3B82F6',
  darkBrandColor: '#1E40AF',
  
  // Settings
  settings: {
    allowGuests: true,
    requiresConfirmation: false,
    showAvailabilityCount: true,
    hideEventTypeList: false,
    enableSEO: true
  },
  
  // Timestamps
  createdAt: new Date('2023-01-15T10:00:00Z'),
  updatedAt: new Date('2024-12-01T15:30:00Z')
};
```

## Event Types Data

### Consultation Event Types
```typescript
const MOCK_EVENT_TYPES: EventType[] = [
  {
    id: 'evt_001',
    title: 'Strategy Consultation',
    slug: 'strategy-consultation',
    description: 'A comprehensive 60-minute session to discuss your marketing strategy, identify growth opportunities, and create an actionable plan.',
    length: 60,
    price: 150,
    currency: 'USD',
    
    // Scheduling
    scheduleId: 'schedule_001',
    minimumBookingNotice: 1440, // 24 hours
    maximumBookingNotice: 8640, // 60 days
    slotInterval: 60,
    
    // Buffers
    offsetStart: 15, // 15 min buffer before
    offsetEnd: 15,   // 15 min buffer after
    
    // Locations
    locations: [
      {
        type: 'integrations:zoom',
        displayLocationPublicly: false,
        credentialId: 'zoom_001'
      },
      {
        type: 'phone',
        phone: '+****************',
        displayLocationPublicly: true
      }
    ],
    
    // Form fields
    bookingFields: [
      {
        name: 'name',
        type: 'text',
        label: 'Full Name',
        required: true,
        placeholder: 'Enter your full name'
      },
      {
        name: 'email',
        type: 'email',
        label: 'Email Address',
        required: true,
        placeholder: '<EMAIL>'
      },
      {
        name: 'company',
        type: 'text',
        label: 'Company Name',
        required: false,
        placeholder: 'Your company name'
      },
      {
        name: 'goals',
        type: 'textarea',
        label: 'What are your main marketing goals?',
        required: true,
        placeholder: 'Please describe your current challenges and what you hope to achieve...'
      },
      {
        name: 'budget',
        type: 'select',
        label: 'Monthly Marketing Budget',
        required: false,
        options: ['Under $5K', '$5K - $15K', '$15K - $50K', '$50K+', 'Prefer not to say']
      }
    ],
    
    // Settings
    hidden: false,
    disabled: false,
    requiresConfirmation: false,
    disableGuests: false,
    
    // Metadata
    color: '#3B82F6',
    metadata: {
      title: 'Book a Strategy Consultation with Sarah',
      description: 'Get expert marketing advice tailored to your business needs.',
      successRedirectUrl: 'https://sarahjohnson.consulting/thank-you'
    },
    
    // Stats (for demo)
    bookingCount: 47,
    lastBooking: new Date('2024-12-10T14:00:00Z'),
    
    createdAt: new Date('2023-02-01T09:00:00Z'),
    updatedAt: new Date('2024-11-15T16:20:00Z')
  },
  
  {
    id: 'evt_002',
    title: 'Quick Chat',
    slug: 'quick-chat',
    description: 'A brief 15-minute call to discuss your questions or explore how we might work together.',
    length: 15,
    price: 0,
    currency: 'USD',
    
    scheduleId: 'schedule_001',
    minimumBookingNotice: 120, // 2 hours
    maximumBookingNotice: 2160, // 15 days
    slotInterval: 15,
    
    offsetStart: 5,
    offsetEnd: 5,
    
    locations: [
      {
        type: 'phone',
        phone: '+****************',
        displayLocationPublicly: true
      },
      {
        type: 'integrations:google:meet',
        displayLocationPublicly: false,
        credentialId: 'google_001'
      }
    ],
    
    bookingFields: [
      {
        name: 'name',
        type: 'text',
        label: 'Full Name',
        required: true
      },
      {
        name: 'email',
        type: 'email',
        label: 'Email Address',
        required: true
      },
      {
        name: 'topic',
        type: 'text',
        label: 'What would you like to discuss?',
        required: true,
        placeholder: 'Brief description of your question or topic'
      }
    ],
    
    hidden: false,
    disabled: false,
    requiresConfirmation: false,
    disableGuests: true,
    
    color: '#10B981',
    metadata: {
      title: 'Quick Chat with Sarah',
      description: 'Perfect for quick questions or initial discussions.'
    },
    
    bookingCount: 23,
    lastBooking: new Date('2024-12-09T11:30:00Z'),
    
    createdAt: new Date('2023-03-10T14:00:00Z'),
    updatedAt: new Date('2024-10-22T10:15:00Z')
  },
  
  {
    id: 'evt_003',
    title: 'Workshop: Digital Marketing Fundamentals',
    slug: 'digital-marketing-workshop',
    description: 'A comprehensive 2-hour workshop covering the fundamentals of digital marketing, including SEO, social media, and content strategy.',
    length: 120,
    price: 75,
    currency: 'USD',
    
    scheduleId: 'schedule_002', // Different schedule for workshops
    minimumBookingNotice: 4320, // 3 days
    maximumBookingNotice: 14400, // 10 days
    slotInterval: 120,
    
    offsetStart: 10,
    offsetEnd: 10,
    
    // Group event settings
    seatsPerTimeSlot: 8,
    seatsShowAttendees: false,
    seatsShowAvailabilityCount: true,
    
    locations: [
      {
        type: 'integrations:zoom',
        displayLocationPublicly: false,
        credentialId: 'zoom_001'
      }
    ],
    
    bookingFields: [
      {
        name: 'name',
        type: 'text',
        label: 'Full Name',
        required: true
      },
      {
        name: 'email',
        type: 'email',
        label: 'Email Address',
        required: true
      },
      {
        name: 'experience',
        type: 'select',
        label: 'Your marketing experience level',
        required: true,
        options: ['Beginner', 'Intermediate', 'Advanced']
      },
      {
        name: 'interests',
        type: 'multiselect',
        label: 'Areas of interest (select all that apply)',
        required: false,
        options: ['SEO', 'Social Media', 'Content Marketing', 'Email Marketing', 'PPC Advertising', 'Analytics']
      }
    ],
    
    hidden: false,
    disabled: false,
    requiresConfirmation: true,
    disableGuests: false,
    
    color: '#8B5CF6',
    metadata: {
      title: 'Digital Marketing Workshop with Sarah',
      description: 'Learn the fundamentals of digital marketing in this interactive workshop.',
      successRedirectUrl: 'https://sarahjohnson.consulting/workshop-prep'
    },
    
    bookingCount: 12,
    lastBooking: new Date('2024-12-05T10:00:00Z'),
    
    createdAt: new Date('2023-06-15T11:00:00Z'),
    updatedAt: new Date('2024-11-30T09:45:00Z')
  }
];
```

## Availability & Schedules

### Default Business Schedule
```typescript
const MOCK_SCHEDULES: Schedule[] = [
  {
    id: 'schedule_001',
    name: 'Business Hours',
    timezone: 'America/New_York',
    isDefault: true,
    
    // Monday to Friday, 9 AM to 5 PM
    availability: [
      { day: 0, enabled: false, timeRanges: [] }, // Sunday
      { day: 1, enabled: true, timeRanges: [{ start: '09:00', end: '17:00' }] }, // Monday
      { day: 2, enabled: true, timeRanges: [{ start: '09:00', end: '17:00' }] }, // Tuesday
      { day: 3, enabled: true, timeRanges: [{ start: '09:00', end: '17:00' }] }, // Wednesday
      { day: 4, enabled: true, timeRanges: [{ start: '09:00', end: '17:00' }] }, // Thursday
      { day: 5, enabled: true, timeRanges: [{ start: '09:00', end: '17:00' }] }, // Friday
      { day: 6, enabled: false, timeRanges: [] }, // Saturday
    ],
    
    // Date-specific overrides
    dateOverrides: [
      {
        id: 'override_001',
        date: '2024-12-25',
        type: 'unavailable',
        reason: 'Christmas Day'
      },
      {
        id: 'override_002',
        date: '2024-12-31',
        type: 'custom_hours',
        timeRanges: [{ start: '09:00', end: '13:00' }],
        reason: 'New Year\'s Eve - Half Day'
      },
      {
        id: 'override_003',
        date: '2024-12-20',
        type: 'unavailable',
        reason: 'Personal Day'
      }
    ],
    
    createdAt: new Date('2023-01-15T10:00:00Z'),
    updatedAt: new Date('2024-11-01T14:30:00Z')
  },
  
  {
    id: 'schedule_002',
    name: 'Workshop Schedule',
    timezone: 'America/New_York',
    isDefault: false,
    
    // Workshops only on Tuesdays and Thursdays, 10 AM to 4 PM
    availability: [
      { day: 0, enabled: false, timeRanges: [] },
      { day: 1, enabled: false, timeRanges: [] },
      { day: 2, enabled: true, timeRanges: [{ start: '10:00', end: '16:00' }] }, // Tuesday
      { day: 3, enabled: false, timeRanges: [] },
      { day: 4, enabled: true, timeRanges: [{ start: '10:00', end: '16:00' }] }, // Thursday
      { day: 5, enabled: false, timeRanges: [] },
      { day: 6, enabled: false, timeRanges: [] },
    ],
    
    dateOverrides: [],
    
    createdAt: new Date('2023-06-15T11:00:00Z'),
    updatedAt: new Date('2024-09-10T16:20:00Z')
  }
];
```

## Bookings Data

### Sample Bookings
```typescript
const MOCK_BOOKINGS: Booking[] = [
  {
    id: 'booking_001',
    uid: 'bk_abc123def456',
    eventTypeId: 'evt_001',
    
    // Timing
    startTime: new Date('2024-12-15T14:00:00Z'),
    endTime: new Date('2024-12-15T15:00:00Z'),
    timezone: 'America/New_York',
    
    // Attendee information
    attendees: [
      {
        id: 'attendee_001',
        name: 'Michael Chen',
        email: '<EMAIL>',
        phone: '+****************',
        noShow: false,
        responses: {
          company: 'TechStartup Inc.',
          goals: 'We need help scaling our content marketing efforts and improving our lead generation.',
          budget: '$15K - $50K'
        }
      }
    ],
    
    // Booking details
    status: 'confirmed',
    location: {
      type: 'integrations:zoom',
      meetingUrl: 'https://zoom.us/j/*********',
      meetingId: '***********',
      password: 'marketing2024'
    },
    
    // Metadata
    notes: 'Looking forward to discussing content strategy and lead generation optimization.',
    confirmationNumber: 'CONF-2024-001',
    
    // System fields
    createdAt: new Date('2024-12-10T09:30:00Z'),
    updatedAt: new Date('2024-12-10T09:30:00Z'),
    
    // Payment (if applicable)
    payment: {
      status: 'paid',
      amount: 150,
      currency: 'USD',
      paymentId: 'pay_*********0',
      paidAt: new Date('2024-12-10T09:31:00Z')
    }
  },
  
  {
    id: 'booking_002',
    uid: 'bk_def456ghi789',
    eventTypeId: 'evt_002',
    
    startTime: new Date('2024-12-12T16:30:00Z'),
    endTime: new Date('2024-12-12T16:45:00Z'),
    timezone: 'America/New_York',
    
    attendees: [
      {
        id: 'attendee_002',
        name: 'Lisa Rodriguez',
        email: '<EMAIL>',
        phone: '+****************',
        noShow: false,
        responses: {
          topic: 'Interested in discussing social media strategy for our design agency'
        }
      }
    ],
    
    status: 'confirmed',
    location: {
      type: 'phone',
      phone: '+****************'
    },
    
    notes: 'Quick call to explore social media opportunities.',
    confirmationNumber: 'CONF-2024-002',
    
    createdAt: new Date('2024-12-11T14:20:00Z'),
    updatedAt: new Date('2024-12-11T14:20:00Z')
  },
  
  {
    id: 'booking_003',
    uid: 'bk_ghi789jkl012',
    eventTypeId: 'evt_003',
    
    startTime: new Date('2024-12-17T15:00:00Z'),
    endTime: new Date('2024-12-17T17:00:00Z'),
    timezone: 'America/New_York',
    
    attendees: [
      {
        id: 'attendee_003',
        name: 'David Kim',
        email: '<EMAIL>',
        responses: {
          experience: 'Beginner',
          interests: ['SEO', 'Social Media', 'Content Marketing']
        }
      },
      {
        id: 'attendee_004',
        name: 'Jennifer Walsh',
        email: '<EMAIL>',
        responses: {
          experience: 'Intermediate',
          interests: ['Email Marketing', 'Analytics']
        }
      }
    ],
    
    status: 'confirmed',
    location: {
      type: 'integrations:zoom',
      meetingUrl: 'https://zoom.us/j/987654321',
      meetingId: '987 654 321',
      password: 'workshop2024'
    },
    
    notes: 'Team from retail business looking to improve their digital marketing.',
    confirmationNumber: 'CONF-2024-003',
    
    createdAt: new Date('2024-12-14T11:45:00Z'),
    updatedAt: new Date('2024-12-14T11:45:00Z'),
    
    payment: {
      status: 'paid',
      amount: 150, // $75 x 2 attendees
      currency: 'USD',
      paymentId: 'pay_0987654321',
      paidAt: new Date('2024-12-14T11:46:00Z')
    }
  },
  
  // Past booking
  {
    id: 'booking_004',
    uid: 'bk_jkl012mno345',
    eventTypeId: 'evt_001',
    
    startTime: new Date('2024-12-08T10:00:00Z'),
    endTime: new Date('2024-12-08T11:00:00Z'),
    timezone: 'America/New_York',
    
    attendees: [
      {
        id: 'attendee_005',
        name: 'Amanda Foster',
        email: '<EMAIL>',
        responses: {
          company: 'Community Nonprofit Organization',
          goals: 'Need help with digital fundraising and donor engagement strategies.',
          budget: 'Under $5K'
        }
      }
    ],
    
    status: 'completed',
    location: {
      type: 'integrations:zoom',
      meetingUrl: 'https://zoom.us/j/*********',
      meetingId: '***********'
    },
    
    notes: 'Great session! Provided comprehensive fundraising strategy recommendations.',
    confirmationNumber: 'CONF-2024-004',
    
    createdAt: new Date('2024-12-05T16:30:00Z'),
    updatedAt: new Date('2024-12-08T11:05:00Z'),
    
    payment: {
      status: 'paid',
      amount: 150,
      currency: 'USD',
      paymentId: 'pay_5555666677',
      paidAt: new Date('2024-12-05T16:31:00Z')
    }
  },
  
  // Cancelled booking
  {
    id: 'booking_005',
    uid: 'bk_mno345pqr678',
    eventTypeId: 'evt_002',
    
    startTime: new Date('2024-12-13T13:15:00Z'),
    endTime: new Date('2024-12-13T13:30:00Z'),
    timezone: 'America/New_York',
    
    attendees: [
      {
        id: 'attendee_006',
        name: 'Robert Thompson',
        email: '<EMAIL>',
        responses: {
          topic: 'Partnership opportunities discussion'
        }
      }
    ],
    
    status: 'cancelled',
    location: {
      type: 'phone',
      phone: '+****************'
    },
    
    notes: 'Cancelled due to scheduling conflict. Rescheduled for next week.',
    confirmationNumber: 'CONF-2024-005',
    cancellationReason: 'Scheduling conflict',
    cancelledAt: new Date('2024-12-12T08:30:00Z'),
    
    createdAt: new Date('2024-12-11T19:15:00Z'),
    updatedAt: new Date('2024-12-12T08:30:00Z')
  }
];
```

## Data Generation Utilities

### Mock Data Factory
```typescript
interface MockDataFactory {
  // Generate additional bookings
  generateBookings(count: number, eventTypeId?: string): Booking[];
  
  // Generate realistic attendee data
  generateAttendee(): AttendeeInfo;
  
  // Generate time slots for date range
  generateTimeSlots(startDate: Date, endDate: Date, eventType: EventType): TimeSlot[];
  
  // Generate availability data
  generateAvailability(schedule: Schedule, dateRange: DateRange): AvailabilityData;
  
  // Utility functions
  randomDate(start: Date, end: Date): Date;
  randomChoice<T>(array: T[]): T;
  randomName(): { first: string; last: string; full: string };
  randomEmail(name: string): string;
  randomCompany(): string;
}

// Sample names for realistic data generation
const SAMPLE_NAMES = {
  first: ['Alex', 'Jordan', 'Taylor', 'Morgan', 'Casey', 'Riley', 'Avery', 'Quinn'],
  last: ['Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez']
};

const SAMPLE_COMPANIES = [
  'TechFlow Solutions', 'Creative Digital Agency', 'Growth Marketing Co.',
  'Startup Innovations', 'E-commerce Plus', 'Data Analytics Pro',
  'Social Media Experts', 'Content Strategy Group'
];
```

This comprehensive mock data structure provides a realistic foundation for development and testing of the appointment booking module, ensuring all components have appropriate sample data to work with.
