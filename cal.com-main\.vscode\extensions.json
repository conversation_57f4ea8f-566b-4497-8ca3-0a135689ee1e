{
  "recommendations": [
    "DavidAnson.vscode-markdownlint", // markdown linting
    "yzhang.markdown-all-in-one", // nicer markdown support
    "esbenp.prettier-vscode", // prettier plugin
    "dbaeumer.vscode-eslint", // eslint plugin
    "bradlc.vscode-tailwindcss", // hinting / autocompletion for tailwind
    "ban.spellright", // Spell check for docs
    "stripe.vscode-stripe", // stripe VSCode extension
    "Prisma.prisma", // syntax|format|completion for prisma
    "rebornix.project-snippets", // Share useful snippets between collaborators
    "inlang.vs-code-extension" // improved i18n DX
  ]
}
