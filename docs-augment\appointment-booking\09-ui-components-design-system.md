# UI Components & Design System - Appointment Booking Module

## Table of Contents
1. [Design System Overview](#design-system-overview)
2. [Component Library](#component-library)
3. [Design Tokens](#design-tokens)
4. [Layout Components](#layout-components)
5. [Form Components](#form-components)
6. [Accessibility Guidelines](#accessibility-guidelines)

## Design System Overview

The appointment booking module follows the existing CRM design system while introducing booking-specific components. All components are built with accessibility, responsiveness, and consistency in mind.

### Design Principles
- **Consistency**: Reuse existing CRM patterns and components
- **Accessibility**: WCAG 2.1 AA compliance
- **Responsiveness**: Mobile-first design approach
- **Performance**: Optimized rendering and minimal bundle size
- **Maintainability**: Clear component APIs and documentation

### Component Architecture
```typescript
interface ComponentArchitecture {
  // Base components (from existing CRM)
  base: {
    Button: ComponentType;
    Card: ComponentType;
    Input: ComponentType;
    Select: ComponentType;
    Dialog: ComponentType;
  };
  
  // Booking-specific components
  booking: {
    BookingCard: ComponentType;
    TimeSlotButton: ComponentType;
    CalendarDay: ComponentType;
    AvailabilityIndicator: ComponentType;
    BookingForm: ComponentType;
  };
  
  // Composite components
  composite: {
    Booker: ComponentType;
    EventTypeEditor: ComponentType;
    ScheduleEditor: ComponentType;
    BookingListView: ComponentType;
  };
}
```

## Component Library

### Core Booking Components

#### BookingCard Component
```typescript
interface BookingCardProps {
  booking: Booking;
  variant: 'compact' | 'standard' | 'detailed';
  showActions?: boolean;
  isSelected?: boolean;
  onClick?: () => void;
  onAction?: (action: BookingAction) => void;
}

const BookingCard: React.FC<BookingCardProps> = ({
  booking,
  variant = 'standard',
  showActions = true,
  isSelected = false,
  onClick,
  onAction
}) => {
  const cardClasses = cn(
    'booking-card',
    'bg-white dark:bg-gray-800',
    'border border-gray-200 dark:border-gray-700',
    'rounded-lg shadow-sm',
    'hover:shadow-md transition-shadow',
    'cursor-pointer',
    {
      'ring-2 ring-blue-500': isSelected,
      'p-3': variant === 'compact',
      'p-4': variant === 'standard',
      'p-6': variant === 'detailed'
    }
  );
  
  return (
    <div className={cardClasses} onClick={onClick}>
      <BookingCardHeader booking={booking} variant={variant} />
      <BookingCardContent booking={booking} variant={variant} />
      {showActions && (
        <BookingCardActions booking={booking} onAction={onAction} />
      )}
    </div>
  );
};
```

#### TimeSlotButton Component
```typescript
interface TimeSlotButtonProps {
  time: string;
  available: boolean;
  selected?: boolean;
  attendees?: number;
  maxAttendees?: number;
  price?: number;
  duration?: number;
  onClick: () => void;
  disabled?: boolean;
}

const TimeSlotButton: React.FC<TimeSlotButtonProps> = ({
  time,
  available,
  selected = false,
  attendees = 0,
  maxAttendees,
  price,
  duration,
  onClick,
  disabled = false
}) => {
  const buttonClasses = cn(
    'time-slot-button',
    'w-full p-3 text-sm font-medium',
    'border rounded-md transition-all',
    'focus:outline-none focus:ring-2 focus:ring-blue-500',
    {
      // Available states
      'bg-white border-gray-300 text-gray-900 hover:bg-gray-50': 
        available && !selected && !disabled,
      'bg-blue-600 border-blue-600 text-white': 
        available && selected,
      
      // Unavailable states
      'bg-gray-100 border-gray-200 text-gray-400 cursor-not-allowed': 
        !available || disabled,
      
      // Group booking indicators
      'border-orange-300 bg-orange-50': 
        available && maxAttendees && attendees > 0
    }
  );
  
  return (
    <button
      className={buttonClasses}
      onClick={onClick}
      disabled={!available || disabled}
      aria-label={`Book appointment at ${time}${price ? ` for $${price}` : ''}`}
    >
      <div className="flex flex-col items-center space-y-1">
        <span className="font-semibold">{formatTime(time)}</span>
        
        {price && price > 0 && (
          <span className="text-xs text-gray-600">${price}</span>
        )}
        
        {maxAttendees && (
          <span className="text-xs text-gray-500">
            {attendees}/{maxAttendees} booked
          </span>
        )}
        
        {duration && duration !== 30 && (
          <span className="text-xs text-gray-500">{duration}min</span>
        )}
      </div>
    </button>
  );
};
```

#### CalendarDay Component
```typescript
interface CalendarDayProps {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  available: boolean;
  slotsCount?: number;
  events?: CalendarEvent[];
  onClick: (date: Date) => void;
  onEventClick?: (event: CalendarEvent) => void;
}

const CalendarDay: React.FC<CalendarDayProps> = ({
  date,
  isCurrentMonth,
  isToday,
  isSelected,
  available,
  slotsCount = 0,
  events = [],
  onClick,
  onEventClick
}) => {
  const dayClasses = cn(
    'calendar-day',
    'relative w-full h-full min-h-[40px] p-1',
    'border border-gray-200 dark:border-gray-700',
    'cursor-pointer transition-colors',
    'focus:outline-none focus:ring-2 focus:ring-blue-500',
    {
      'bg-white dark:bg-gray-800': isCurrentMonth,
      'bg-gray-50 dark:bg-gray-900 text-gray-400': !isCurrentMonth,
      'bg-blue-50 border-blue-300': isSelected,
      'bg-yellow-50 border-yellow-300': isToday,
      'hover:bg-gray-50 dark:hover:bg-gray-700': available && isCurrentMonth,
      'cursor-not-allowed opacity-50': !available
    }
  );
  
  return (
    <div
      className={dayClasses}
      onClick={() => available && onClick(date)}
      role="gridcell"
      aria-label={`${format(date, 'MMMM d, yyyy')}${available ? ', available' : ', unavailable'}`}
      tabIndex={isCurrentMonth ? 0 : -1}
    >
      <div className="flex flex-col h-full">
        <div className="flex items-center justify-between">
          <span className={cn('text-sm font-medium', {
            'text-blue-600': isToday,
            'text-gray-900 dark:text-gray-100': !isToday && isCurrentMonth,
            'text-gray-400': !isCurrentMonth
          })}>
            {format(date, 'd')}
          </span>
          
          {available && slotsCount > 0 && (
            <AvailabilityIndicator count={slotsCount} />
          )}
        </div>
        
        <div className="flex-1 mt-1">
          {events.slice(0, 3).map((event, index) => (
            <CalendarEventDot
              key={event.id}
              event={event}
              onClick={() => onEventClick?.(event)}
              style={{ top: `${index * 6}px` }}
            />
          ))}
          
          {events.length > 3 && (
            <span className="text-xs text-gray-500">
              +{events.length - 3} more
            </span>
          )}
        </div>
      </div>
    </div>
  );
};
```

#### AvailabilityIndicator Component
```typescript
interface AvailabilityIndicatorProps {
  count: number;
  maxCount?: number;
  variant?: 'dot' | 'badge' | 'text';
  size?: 'sm' | 'md' | 'lg';
}

const AvailabilityIndicator: React.FC<AvailabilityIndicatorProps> = ({
  count,
  maxCount = 10,
  variant = 'badge',
  size = 'sm'
}) => {
  if (count === 0) return null;
  
  const intensity = Math.min(count / maxCount, 1);
  
  switch (variant) {
    case 'dot':
      return (
        <div
          className={cn('rounded-full', {
            'w-2 h-2': size === 'sm',
            'w-3 h-3': size === 'md',
            'w-4 h-4': size === 'lg'
          })}
          style={{
            backgroundColor: `rgba(34, 197, 94, ${0.3 + intensity * 0.7})`
          }}
          title={`${count} available slots`}
        />
      );
      
    case 'badge':
      return (
        <span
          className={cn(
            'inline-flex items-center justify-center',
            'rounded-full text-white font-medium',
            {
              'text-xs px-1.5 py-0.5 min-w-[18px] h-[18px]': size === 'sm',
              'text-sm px-2 py-1 min-w-[24px] h-[24px]': size === 'md',
              'text-base px-2.5 py-1.5 min-w-[32px] h-[32px]': size === 'lg'
            }
          )}
          style={{
            backgroundColor: `rgba(34, 197, 94, ${0.6 + intensity * 0.4})`
          }}
        >
          {count > 99 ? '99+' : count}
        </span>
      );
      
    case 'text':
      return (
        <span className={cn('text-green-600 font-medium', {
          'text-xs': size === 'sm',
          'text-sm': size === 'md',
          'text-base': size === 'lg'
        })}>
          {count} available
        </span>
      );
      
    default:
      return null;
  }
};
```

### Form Components

#### BookingFormField Component
```typescript
interface BookingFormFieldProps {
  field: BookingField;
  value: any;
  onChange: (value: any) => void;
  error?: string;
  disabled?: boolean;
}

const BookingFormField: React.FC<BookingFormFieldProps> = ({
  field,
  value,
  onChange,
  error,
  disabled = false
}) => {
  const fieldId = `field-${field.name}`;
  
  const renderField = () => {
    switch (field.type) {
      case 'text':
      case 'email':
        return (
          <Input
            id={fieldId}
            type={field.type}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            disabled={disabled}
            required={field.required}
            className={cn({ 'border-red-500': error })}
          />
        );
        
      case 'textarea':
        return (
          <Textarea
            id={fieldId}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            disabled={disabled}
            required={field.required}
            rows={3}
            className={cn({ 'border-red-500': error })}
          />
        );
        
      case 'select':
        return (
          <Select
            value={value || ''}
            onValueChange={onChange}
            disabled={disabled}
            required={field.required}
          >
            <SelectTrigger className={cn({ 'border-red-500': error })}>
              <SelectValue placeholder={field.placeholder || 'Select an option'} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );
        
      case 'multiselect':
        return (
          <MultiSelect
            options={field.options || []}
            value={value || []}
            onChange={onChange}
            placeholder={field.placeholder}
            disabled={disabled}
            className={cn({ 'border-red-500': error })}
          />
        );
        
      case 'checkbox':
        return (
          <Checkbox
            id={fieldId}
            checked={value || false}
            onCheckedChange={onChange}
            disabled={disabled}
            required={field.required}
          />
        );
        
      default:
        return null;
    }
  };
  
  return (
    <div className="booking-form-field space-y-2">
      <Label
        htmlFor={fieldId}
        className={cn('text-sm font-medium', {
          'text-red-600': error,
          'after:content-["*"] after:text-red-500 after:ml-1': field.required
        })}
      >
        {field.label}
      </Label>
      
      {renderField()}
      
      {error && (
        <p className="text-sm text-red-600" role="alert">
          {error}
        </p>
      )}
    </div>
  );
};
```

## Design Tokens

### Color System
```typescript
const BOOKING_COLORS = {
  // Status colors
  status: {
    confirmed: '#10B981',    // green-500
    pending: '#F59E0B',      // amber-500
    cancelled: '#EF4444',    // red-500
    completed: '#6B7280',    // gray-500
    noShow: '#F97316'        // orange-500
  },
  
  // Availability colors
  availability: {
    available: '#10B981',    // green-500
    unavailable: '#EF4444',  // red-500
    busy: '#F59E0B',         // amber-500
    tentative: '#8B5CF6'     // violet-500
  },
  
  // Event type colors
  eventTypes: [
    '#3B82F6', // blue-500
    '#10B981', // green-500
    '#F59E0B', // amber-500
    '#EF4444', // red-500
    '#8B5CF6', // violet-500
    '#EC4899', // pink-500
    '#06B6D4', // cyan-500
    '#84CC16'  // lime-500
  ],
  
  // Calendar colors
  calendar: {
    today: '#FEF3C7',        // amber-100
    selected: '#DBEAFE',     // blue-100
    weekend: '#F9FAFB',      // gray-50
    otherMonth: '#F3F4F6'    // gray-100
  }
};
```

### Typography Scale
```typescript
const BOOKING_TYPOGRAPHY = {
  // Headings
  h1: 'text-3xl font-bold tracking-tight',
  h2: 'text-2xl font-semibold tracking-tight',
  h3: 'text-xl font-semibold',
  h4: 'text-lg font-semibold',
  
  // Body text
  body: 'text-base',
  bodySmall: 'text-sm',
  caption: 'text-xs',
  
  // Interactive elements
  button: 'text-sm font-medium',
  link: 'text-sm font-medium text-blue-600 hover:text-blue-500',
  
  // Form elements
  label: 'text-sm font-medium text-gray-700',
  input: 'text-sm',
  placeholder: 'text-sm text-gray-500',
  error: 'text-xs text-red-600'
};
```

### Spacing System
```typescript
const BOOKING_SPACING = {
  // Component spacing
  cardPadding: {
    compact: 'p-3',
    standard: 'p-4',
    detailed: 'p-6'
  },
  
  // Layout spacing
  sectionGap: 'space-y-6',
  itemGap: 'space-y-4',
  fieldGap: 'space-y-2',
  
  // Grid spacing
  gridGap: {
    tight: 'gap-2',
    normal: 'gap-4',
    loose: 'gap-6'
  }
};
```

### Animation System
```typescript
const BOOKING_ANIMATIONS = {
  // Transitions
  transition: 'transition-all duration-200 ease-in-out',
  transitionFast: 'transition-all duration-150 ease-in-out',
  transitionSlow: 'transition-all duration-300 ease-in-out',
  
  // Hover effects
  hover: 'hover:shadow-md hover:scale-105',
  hoverSubtle: 'hover:bg-gray-50',
  
  // Focus effects
  focus: 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
  
  // Loading animations
  pulse: 'animate-pulse',
  spin: 'animate-spin',
  bounce: 'animate-bounce'
};
```

## Layout Components

### BookingLayout Component
```typescript
interface BookingLayoutProps {
  children: React.ReactNode;
  sidebar?: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  variant?: 'default' | 'fullscreen' | 'modal';
}

const BookingLayout: React.FC<BookingLayoutProps> = ({
  children,
  sidebar,
  header,
  footer,
  variant = 'default'
}) => {
  const layoutClasses = cn('booking-layout', {
    'min-h-screen bg-gray-50': variant === 'default',
    'fixed inset-0 bg-white': variant === 'fullscreen',
    'relative bg-white rounded-lg': variant === 'modal'
  });
  
  return (
    <div className={layoutClasses}>
      {header && (
        <header className="booking-header border-b border-gray-200">
          {header}
        </header>
      )}
      
      <div className="booking-content flex flex-1">
        {sidebar && (
          <aside className="booking-sidebar w-64 border-r border-gray-200 bg-white">
            {sidebar}
          </aside>
        )}
        
        <main className="booking-main flex-1 overflow-auto">
          {children}
        </main>
      </div>
      
      {footer && (
        <footer className="booking-footer border-t border-gray-200">
          {footer}
        </footer>
      )}
    </div>
  );
};
```

## Accessibility Guidelines

### ARIA Labels and Roles
```typescript
const ACCESSIBILITY_PATTERNS = {
  // Calendar navigation
  calendar: {
    role: 'grid',
    'aria-label': 'Calendar',
    'aria-live': 'polite'
  },
  
  calendarDay: {
    role: 'gridcell',
    'aria-selected': 'boolean',
    'aria-label': 'Date with availability status'
  },
  
  // Time slot selection
  timeSlot: {
    role: 'button',
    'aria-pressed': 'boolean',
    'aria-label': 'Time slot with price and availability'
  },
  
  // Form fields
  formField: {
    'aria-required': 'boolean',
    'aria-invalid': 'boolean',
    'aria-describedby': 'error-id'
  },
  
  // Status indicators
  status: {
    role: 'status',
    'aria-live': 'polite'
  }
};
```

### Keyboard Navigation
```typescript
const KEYBOARD_HANDLERS = {
  // Calendar navigation
  calendarKeyDown: (event: KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowLeft':
        navigateDate(-1);
        break;
      case 'ArrowRight':
        navigateDate(1);
        break;
      case 'ArrowUp':
        navigateDate(-7);
        break;
      case 'ArrowDown':
        navigateDate(7);
        break;
      case 'Enter':
      case ' ':
        selectDate();
        break;
      case 'Home':
        goToFirstDayOfWeek();
        break;
      case 'End':
        goToLastDayOfWeek();
        break;
    }
  },
  
  // Time slot navigation
  timeSlotKeyDown: (event: KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowUp':
      case 'ArrowLeft':
        focusPreviousSlot();
        break;
      case 'ArrowDown':
      case 'ArrowRight':
        focusNextSlot();
        break;
      case 'Enter':
      case ' ':
        selectTimeSlot();
        break;
    }
  }
};
```

This comprehensive UI component and design system documentation ensures consistent, accessible, and maintainable user interfaces throughout the appointment booking module.
