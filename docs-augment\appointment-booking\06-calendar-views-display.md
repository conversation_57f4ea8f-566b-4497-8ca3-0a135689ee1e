# Calendar Views & Display - Appointment Booking Module

## Table of Contents
1. [View Types Overview](#view-types-overview)
2. [Booking List View](#booking-list-view)
3. [Booking Grid View](#booking-grid-view)
4. [Full Calendar View](#full-calendar-view)
5. [Weekly Calendar View](#weekly-calendar-view)
6. [View State Management](#view-state-management)

## View Types Overview

The appointment booking module supports multiple display modes to accommodate different user preferences and use cases. Each view provides unique advantages for managing and visualizing bookings.

### Available Views
```typescript
enum CalendarViewType {
  LIST = 'list',           // Chronological list of bookings
  GRID = 'grid',           // Card-based grid layout
  CALENDAR_MONTH = 'month', // Traditional monthly calendar
  CALENDAR_WEEK = 'week',   // Weekly calendar view
  CALENDAR_DAY = 'day'      // Single day detailed view
}

interface ViewConfiguration {
  type: CalendarViewType;
  defaultFilters: BookingFilters;
  sortOptions: SortOption[];
  displayOptions: DisplayOption[];
  responsiveBreakpoints: ResponsiveConfig;
}
```

### Common View Features
- **Responsive Design**: Adapts to mobile, tablet, and desktop
- **Real-time Updates**: Live booking status changes
- **Filtering & Search**: Advanced filtering capabilities
- **Bulk Operations**: Multi-select actions
- **Export Functions**: PDF, CSV, and calendar exports
- **Accessibility**: Full keyboard navigation and screen reader support

## Booking List View

### Component Structure
```typescript
interface BookingListViewProps {
  bookings: Booking[];
  filters: BookingFilters;
  sorting: SortConfiguration;
  pagination: PaginationConfig;
  onBookingClick: (booking: Booking) => void;
  onBulkAction: (action: BulkAction, bookingIds: string[]) => void;
  onFilterChange: (filters: BookingFilters) => void;
  onSortChange: (sort: SortConfiguration) => void;
}

interface BookingListItem {
  booking: Booking;
  isSelected: boolean;
  isToday: boolean;
  showRecurringInfo: boolean;
  actions: BookingAction[];
}
```

### List Item Layout
```typescript
interface BookingListItemLayout {
  // Primary information (always visible)
  primary: {
    title: string;
    dateTime: string;
    attendeeName: string;
    status: BookingStatus;
  };
  
  // Secondary information (collapsible on mobile)
  secondary: {
    duration: number;
    location: string;
    attendeeEmail: string;
    eventType: string;
  };
  
  // Actions (context menu or buttons)
  actions: {
    view: () => void;
    edit: () => void;
    reschedule: () => void;
    cancel: () => void;
    markNoShow?: () => void;
  };
}
```

### Filtering Options
```typescript
interface BookingFilters {
  // Date range
  dateRange: {
    start?: Date;
    end?: Date;
    preset?: 'today' | 'week' | 'month' | 'quarter' | 'year';
  };
  
  // Status filters
  status: BookingStatus[];
  
  // Event type filters
  eventTypes: string[];
  
  // Attendee filters
  attendeeSearch?: string;
  
  // Location filters
  locations: string[];
  
  // Custom field filters
  customFields: Record<string, any>;
}

interface SortConfiguration {
  field: 'startTime' | 'createdAt' | 'attendeeName' | 'eventType' | 'status';
  direction: 'asc' | 'desc';
  secondary?: {
    field: string;
    direction: 'asc' | 'desc';
  };
}
```

### List View Features
- **Infinite Scroll**: Load more bookings as user scrolls
- **Virtual Scrolling**: Performance optimization for large datasets
- **Sticky Headers**: Date separators remain visible
- **Quick Actions**: Hover/tap actions for common operations
- **Bulk Selection**: Checkbox selection with bulk operations
- **Search Highlighting**: Highlight search terms in results

### Implementation Example
```typescript
const BookingListView: React.FC<BookingListViewProps> = ({
  bookings,
  filters,
  sorting,
  onBookingClick,
  onBulkAction
}) => {
  const [selectedBookings, setSelectedBookings] = useState<Set<string>>(new Set());
  const [expandedBookings, setExpandedBookings] = useState<Set<string>>(new Set());
  
  const groupedBookings = useMemo(() => {
    return groupBookingsByDate(bookings, sorting);
  }, [bookings, sorting]);
  
  return (
    <div className="booking-list-view">
      <BookingListHeader 
        filters={filters}
        sorting={sorting}
        selectedCount={selectedBookings.size}
        onBulkAction={onBulkAction}
      />
      
      <div className="booking-list-content">
        {groupedBookings.map(group => (
          <BookingDateGroup
            key={group.date}
            date={group.date}
            bookings={group.bookings}
            selectedBookings={selectedBookings}
            expandedBookings={expandedBookings}
            onBookingClick={onBookingClick}
            onSelectionChange={setSelectedBookings}
          />
        ))}
      </div>
    </div>
  );
};
```

## Booking Grid View

### Grid Layout Configuration
```typescript
interface GridViewConfig {
  columns: {
    mobile: number;    // 1-2 columns
    tablet: number;    // 2-3 columns  
    desktop: number;   // 3-4 columns
    wide: number;      // 4-6 columns
  };
  cardSize: 'compact' | 'standard' | 'detailed';
  spacing: 'tight' | 'normal' | 'loose';
  aspectRatio?: number;
}

interface BookingCard {
  booking: Booking;
  variant: 'compact' | 'standard' | 'detailed';
  showImage: boolean;
  showActions: boolean;
  isSelected: boolean;
}
```

### Card Variants
```typescript
// Compact Card (mobile-optimized)
interface CompactBookingCard {
  title: string;
  dateTime: string;
  attendee: string;
  status: BookingStatus;
  quickActions: ['view', 'reschedule'];
}

// Standard Card (default)
interface StandardBookingCard {
  title: string;
  dateTime: string;
  duration: number;
  attendee: {
    name: string;
    email: string;
    avatar?: string;
  };
  location: string;
  status: BookingStatus;
  actions: BookingAction[];
}

// Detailed Card (desktop)
interface DetailedBookingCard {
  title: string;
  description?: string;
  dateTime: string;
  duration: number;
  attendee: {
    name: string;
    email: string;
    phone?: string;
    avatar?: string;
  };
  location: LocationInfo;
  status: BookingStatus;
  metadata: {
    createdAt: Date;
    confirmationNumber: string;
    notes?: string;
  };
  actions: BookingAction[];
}
```

### Grid Features
- **Masonry Layout**: Cards adjust height based on content
- **Drag & Drop**: Reorder and reschedule by dragging
- **Lazy Loading**: Load images and content as needed
- **Hover Effects**: Preview additional information
- **Responsive Grid**: Automatic column adjustment
- **Card Animations**: Smooth transitions and micro-interactions

## Full Calendar View

### Calendar Component Structure
```typescript
interface FullCalendarViewProps {
  bookings: Booking[];
  view: 'month' | 'week' | 'day';
  currentDate: Date;
  onDateChange: (date: Date) => void;
  onViewChange: (view: CalendarView) => void;
  onBookingClick: (booking: Booking) => void;
  onDateClick: (date: Date) => void;
  onTimeSlotClick?: (date: Date, time: string) => void;
}

interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  allDay: boolean;
  color?: string;
  textColor?: string;
  borderColor?: string;
  className?: string;
  booking: Booking;
}
```

### Month View Layout
```typescript
interface MonthViewConfig {
  showWeekNumbers: boolean;
  showOtherMonthDays: boolean;
  fixedWeekCount: boolean;
  weekStartsOn: 0 | 1; // Sunday or Monday
  
  // Event display
  maxEventsPerDay: number;
  showMoreText: string;
  eventHeight: number;
  eventSpacing: number;
  
  // Interaction
  selectable: boolean;
  selectMirror: boolean;
  unselectAuto: boolean;
}

interface MonthViewCell {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  events: CalendarEvent[];
  availableSlots?: number;
  isClickable: boolean;
}
```

### Week View Layout
```typescript
interface WeekViewConfig {
  slotDuration: string; // '00:30:00' for 30-minute slots
  slotLabelInterval: string; // '01:00:00' for hourly labels
  minTime: string; // '06:00:00' start time
  maxTime: string; // '22:00:00' end time
  
  // Display
  allDaySlot: boolean;
  slotEventOverlap: boolean;
  eventMaxStack: number;
  
  // Interaction
  selectable: boolean;
  selectMirror: boolean;
  snapDuration: string;
}

interface WeekViewTimeSlot {
  time: string;
  date: Date;
  available: boolean;
  events: CalendarEvent[];
  isCurrentTime: boolean;
}
```

### Calendar Features
- **Drag & Drop Rescheduling**: Move bookings between time slots
- **Event Overlapping**: Smart positioning for concurrent events
- **Time Zone Display**: Show multiple time zones
- **Recurring Event Indicators**: Visual cues for recurring bookings
- **Availability Overlay**: Show available time slots
- **Mini Calendar**: Navigation sidebar
- **Event Tooltips**: Hover details without opening full view

## Weekly Calendar View

### Weekly View Specific Features
```typescript
interface WeeklyCalendarProps {
  startDate: Date;
  bookings: Booking[];
  availability: AvailabilityData;
  workingHours: WorkingHours;
  onBookingDrop: (booking: Booking, newStart: Date) => void;
  onTimeSlotClick: (date: Date, time: string) => void;
  showAvailability: boolean;
  showWorkingHours: boolean;
}

interface WorkingHours {
  [dayOfWeek: number]: {
    enabled: boolean;
    start: string; // HH:mm
    end: string;   // HH:mm
  };
}
```

### Time Grid Layout
```typescript
interface TimeGridConfig {
  hourHeight: number; // Pixels per hour
  slotDuration: number; // Minutes per slot
  showTimeLabels: boolean;
  show24Hour: boolean;
  
  // Visual styling
  gridLineColor: string;
  workingHoursColor: string;
  availableSlotColor: string;
  bookedSlotColor: string;
}

interface TimeSlotComponent {
  time: string;
  date: Date;
  duration: number;
  available: boolean;
  booked: boolean;
  events: CalendarEvent[];
  onClick: () => void;
  onDrop: (event: CalendarEvent) => void;
}
```

## View State Management

### View Persistence
```typescript
interface ViewState {
  currentView: CalendarViewType;
  currentDate: Date;
  filters: BookingFilters;
  sorting: SortConfiguration;
  selectedBookings: string[];
  expandedSections: string[];
  
  // View-specific state
  gridConfig?: GridViewConfig;
  calendarConfig?: CalendarConfig;
  listConfig?: ListViewConfig;
}

interface ViewStateManager {
  // State management
  getViewState(): ViewState;
  setViewState(state: Partial<ViewState>): void;
  resetViewState(): void;
  
  // Persistence
  saveToLocalStorage(): void;
  loadFromLocalStorage(): ViewState | null;
  
  // URL synchronization
  syncWithURL(): void;
  updateURL(state: ViewState): void;
}
```

### Responsive View Switching
```typescript
interface ResponsiveViewConfig {
  breakpoints: {
    mobile: number;    // < 768px
    tablet: number;    // 768px - 1024px
    desktop: number;   // > 1024px
  };
  
  defaultViews: {
    mobile: CalendarViewType;
    tablet: CalendarViewType;
    desktop: CalendarViewType;
  };
  
  autoSwitch: boolean;
  userOverride: boolean;
}

function useResponsiveView(config: ResponsiveViewConfig) {
  const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  const [currentView, setCurrentView] = useState<CalendarViewType>(CalendarViewType.LIST);
  
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      const newSize = width < config.breakpoints.mobile ? 'mobile' :
                     width < config.breakpoints.desktop ? 'tablet' : 'desktop';
      
      setScreenSize(newSize);
      
      if (config.autoSwitch && !config.userOverride) {
        setCurrentView(config.defaultViews[newSize]);
      }
    };
    
    window.addEventListener('resize', handleResize);
    handleResize();
    
    return () => window.removeEventListener('resize', handleResize);
  }, [config]);
  
  return { screenSize, currentView, setCurrentView };
}
```

### Performance Optimizations
```typescript
interface ViewOptimizations {
  // Virtual scrolling for large datasets
  virtualScrolling: {
    enabled: boolean;
    itemHeight: number;
    overscan: number;
  };
  
  // Lazy loading for calendar events
  lazyLoading: {
    enabled: boolean;
    loadAhead: number; // Days to load ahead
    loadBehind: number; // Days to keep behind
  };
  
  // Memoization strategies
  memoization: {
    bookingGroups: boolean;
    calendarEvents: boolean;
    filterResults: boolean;
  };
  
  // Debouncing for user interactions
  debouncing: {
    search: number; // ms
    filters: number; // ms
    resize: number; // ms
  };
}
```

This comprehensive calendar view system provides flexible, performant, and user-friendly ways to visualize and manage appointment bookings across different devices and use cases.
