# Performance Optimization Guide - Appointment Booking Module

## Table of Contents
1. [Performance Strategy Overview](#performance-strategy-overview)
2. [Data Fetching Optimization](#data-fetching-optimization)
3. [Rendering Performance](#rendering-performance)
4. [Caching Strategies](#caching-strategies)
5. [Bundle Optimization](#bundle-optimization)
6. [Real-time Performance](#real-time-performance)

## Performance Strategy Overview

The appointment booking module implements a comprehensive performance optimization strategy targeting Core Web Vitals and user experience metrics while maintaining functionality and reliability.

### Performance Targets
- **First Contentful Paint**: < 1.5s
- **Largest Contentful Paint**: < 2.5s
- **Cumulative Layout Shift**: < 0.1
- **First Input Delay**: < 100ms
- **Bundle Size**: < 200KB total

### Optimization Principles
- **Lazy Loading**: Load components and data only when needed
- **Strategic Prefetching**: Anticipate user actions and preload critical data
- **Intelligent Caching**: Cache frequently accessed data with smart invalidation
- **Optimistic Updates**: Provide immediate feedback while syncing with server
- **Virtual Scrolling**: Handle large datasets efficiently

## Data Fetching Optimization

### Strategic Prefetching
```typescript
interface PrefetchStrategy {
  // Prefetch next month's availability when user navigates calendar
  prefetchNextMonth: (currentMonth: string) => {
    const nextMonth = addMonths(new Date(currentMonth), 1);
    const prefetchQuery = {
      eventTypeId: selectedEventType.id,
      startDate: format(startOfMonth(nextMonth), 'yyyy-MM-dd'),
      endDate: format(endOfMonth(nextMonth), 'yyyy-MM-dd'),
      timezone: userTimezone
    };
    
    // Prefetch in background with low priority
    queryClient.prefetchQuery({
      queryKey: ['availability', prefetchQuery],
      queryFn: () => availabilityService.getAvailability(prefetchQuery),
      staleTime: 5 * 60 * 1000, // 5 minutes
      priority: 'low'
    });
  };
  
  // Prefetch event type details when hovering over event type cards
  prefetchEventTypeDetails: (eventTypeId: string) => {
    queryClient.prefetchQuery({
      queryKey: ['eventType', eventTypeId],
      queryFn: () => eventTypeService.getEventType(eventTypeId),
      staleTime: 10 * 60 * 1000 // 10 minutes
    });
  };
  
  // Prefetch booking form data when time slot is selected
  prefetchBookingForm: (eventTypeId: string) => {
    queryClient.prefetchQuery({
      queryKey: ['bookingForm', eventTypeId],
      queryFn: () => eventTypeService.getBookingFields(eventTypeId),
      staleTime: 15 * 60 * 1000 // 15 minutes
    });
  };
}
```

### Debounced API Calls
```typescript
// Debounce availability requests during rapid date navigation
const useDebouncedAvailability = (eventTypeId: string, dateRange: DateRange) => {
  const [debouncedDateRange] = useDebounce(dateRange, 300);
  
  return useQuery({
    queryKey: ['availability', eventTypeId, debouncedDateRange],
    queryFn: () => availabilityService.getAvailability({
      eventTypeId,
      ...debouncedDateRange
    }),
    enabled: !!eventTypeId && !!debouncedDateRange,
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 10 * 60 * 1000 // 10 minutes
  });
};

// Debounce search queries
const useDebouncedBookingSearch = (searchTerm: string) => {
  const [debouncedSearch] = useDebounce(searchTerm, 500);
  
  return useQuery({
    queryKey: ['bookings', 'search', debouncedSearch],
    queryFn: () => bookingService.searchBookings(debouncedSearch),
    enabled: debouncedSearch.length >= 2,
    staleTime: 30 * 1000 // 30 seconds
  });
};
```

### Intelligent Data Loading
```typescript
// Load only essential data initially, then progressively enhance
const useProgressiveDataLoading = (eventTypeId: string) => {
  // Essential data - load immediately
  const { data: eventType } = useQuery({
    queryKey: ['eventType', eventTypeId, 'essential'],
    queryFn: () => eventTypeService.getEssentialData(eventTypeId),
    staleTime: 5 * 60 * 1000
  });
  
  // Enhanced data - load after essential data
  const { data: eventTypeDetails } = useQuery({
    queryKey: ['eventType', eventTypeId, 'details'],
    queryFn: () => eventTypeService.getDetailedData(eventTypeId),
    enabled: !!eventType,
    staleTime: 10 * 60 * 1000
  });
  
  // Analytics data - load in background
  const { data: analytics } = useQuery({
    queryKey: ['eventType', eventTypeId, 'analytics'],
    queryFn: () => eventTypeService.getAnalytics(eventTypeId),
    enabled: !!eventType,
    staleTime: 30 * 60 * 1000,
    priority: 'low'
  });
  
  return { eventType, eventTypeDetails, analytics };
};
```

## Rendering Performance

### Component Memoization
```typescript
// Memoize expensive calendar calculations
const CalendarDay = React.memo<CalendarDayProps>(({
  date,
  availability,
  events,
  onDateSelect
}) => {
  const dayAvailability = useMemo(() => 
    calculateDayAvailability(date, availability),
    [date, availability]
  );
  
  const dayEvents = useMemo(() =>
    events.filter(event => isSameDay(event.date, date)),
    [events, date]
  );
  
  return (
    <div 
      className="calendar-day"
      onClick={() => onDateSelect(date)}
    >
      <AvailabilityIndicator availability={dayAvailability} />
      <EventList events={dayEvents} />
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison for optimal re-rendering
  return (
    isSameDay(prevProps.date, nextProps.date) &&
    prevProps.availability === nextProps.availability &&
    prevProps.events.length === nextProps.events.length
  );
});

// Memoize time slot calculations
const TimeSlotGrid = React.memo<TimeSlotGridProps>(({
  slots,
  selectedSlot,
  onSlotSelect
}) => {
  const memoizedSlots = useMemo(() =>
    slots.map(slot => ({
      ...slot,
      isSelected: slot.id === selectedSlot?.id,
      formattedTime: formatTime(slot.time)
    })),
    [slots, selectedSlot]
  );
  
  return (
    <div className="time-slot-grid">
      {memoizedSlots.map(slot => (
        <TimeSlotButton
          key={slot.id}
          slot={slot}
          onSelect={onSlotSelect}
        />
      ))}
    </div>
  );
});
```

### Virtual Scrolling for Large Datasets
```typescript
// Virtual scrolling for booking lists
const VirtualizedBookingList = ({ bookings }: { bookings: Booking[] }) => {
  const parentRef = useRef<HTMLDivElement>(null);
  
  const rowVirtualizer = useVirtualizer({
    count: bookings.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 80, // Estimated row height
    overscan: 5 // Render 5 extra items for smooth scrolling
  });
  
  return (
    <div
      ref={parentRef}
      className="booking-list-container"
      style={{ height: '400px', overflow: 'auto' }}
    >
      <div
        style={{
          height: `${rowVirtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative'
        }}
      >
        {rowVirtualizer.getVirtualItems().map(virtualItem => (
          <div
            key={virtualItem.key}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: `${virtualItem.size}px`,
              transform: `translateY(${virtualItem.start}px)`
            }}
          >
            <BookingListItem booking={bookings[virtualItem.index]} />
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Optimized State Updates
```typescript
// Batch state updates to prevent excessive re-renders
const useOptimizedBookingStore = () => {
  const store = useBookingStore();
  
  const batchUpdateBookings = useCallback((updates: BookingUpdate[]) => {
    // Batch multiple updates into single state change
    store.setState(state => {
      const newBookings = [...state.bookings];
      
      updates.forEach(update => {
        const index = newBookings.findIndex(b => b.id === update.id);
        if (index !== -1) {
          newBookings[index] = { ...newBookings[index], ...update.data };
        }
      });
      
      return { bookings: newBookings };
    });
  }, [store]);
  
  return { ...store, batchUpdateBookings };
};
```

## Caching Strategies

### Multi-level Caching
```typescript
interface CacheStrategy {
  // Level 1: In-memory cache for immediate access
  memoryCache: Map<string, CacheEntry>;
  
  // Level 2: Browser storage for persistence
  browserCache: {
    localStorage: Storage;
    sessionStorage: Storage;
    indexedDB: IDBDatabase;
  };
  
  // Level 3: Service worker cache for offline support
  serviceWorkerCache: Cache;
}

// Intelligent cache with TTL and invalidation
class AvailabilityCache {
  private cache = new Map<string, CacheEntry>();
  private readonly TTL = 5 * 60 * 1000; // 5 minutes
  
  set(key: string, data: AvailabilityData): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: this.TTL
    });
  }
  
  get(key: string): AvailabilityData | null {
    const entry = this.cache.get(key);
    
    if (!entry) return null;
    
    // Check if expired
    if (Date.now() - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }
  
  invalidate(pattern: string): void {
    // Invalidate cache entries matching pattern
    for (const [key] of this.cache) {
      if (key.includes(pattern)) {
        this.cache.delete(key);
      }
    }
  }
  
  // Smart prefetch based on user behavior
  prefetch(eventTypeId: string, currentDate: Date): void {
    const nextMonth = addMonths(currentDate, 1);
    const prefetchKey = `${eventTypeId}-${format(nextMonth, 'yyyy-MM')}`;
    
    if (!this.cache.has(prefetchKey)) {
      // Prefetch next month's availability
      this.fetchAndCache(prefetchKey, {
        eventTypeId,
        startDate: format(startOfMonth(nextMonth), 'yyyy-MM-dd'),
        endDate: format(endOfMonth(nextMonth), 'yyyy-MM-dd')
      });
    }
  }
}
```

### Background Synchronization
```typescript
// Background sync for offline support and performance
const useBackgroundSync = () => {
  const syncQueue = useRef<SyncOperation[]>([]);
  
  const addToSyncQueue = (operation: SyncOperation) => {
    syncQueue.current.push(operation);
    
    // Process queue when online
    if (navigator.onLine) {
      processQueue();
    }
  };
  
  const processQueue = async () => {
    while (syncQueue.current.length > 0) {
      const operation = syncQueue.current.shift();
      
      try {
        await operation.execute();
      } catch (error) {
        // Re-queue failed operations
        syncQueue.current.unshift(operation);
        break;
      }
    }
  };
  
  // Listen for online events
  useEffect(() => {
    const handleOnline = () => processQueue();
    window.addEventListener('online', handleOnline);
    
    return () => window.removeEventListener('online', handleOnline);
  }, []);
  
  return { addToSyncQueue };
};
```

## Bundle Optimization

### Code Splitting Strategy
```typescript
// Route-based code splitting
const BookingPage = lazy(() => import('./pages/BookingPage'));
const EventTypesPage = lazy(() => import('./pages/EventTypesPage'));
const AvailabilityPage = lazy(() => import('./pages/AvailabilityPage'));

// Component-based code splitting for heavy components
const FullCalendarView = lazy(() => import('./components/FullCalendarView'));
const BookingAnalytics = lazy(() => import('./components/BookingAnalytics'));

// Feature-based code splitting
const PaymentIntegration = lazy(() => 
  import('./features/payment').then(module => ({ default: module.PaymentIntegration }))
);

// Dynamic imports for optional features
const loadAdvancedFeatures = async () => {
  const { AdvancedBookingFeatures } = await import('./features/advanced');
  return AdvancedBookingFeatures;
};
```

### Tree Shaking Optimization
```typescript
// Optimize imports to enable tree shaking
import { format, addDays, startOfMonth } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';

// Avoid importing entire libraries
// ❌ import * as dateFns from 'date-fns';
// ✅ import { format, addDays } from 'date-fns';

// Use dynamic imports for large utilities
const loadDateUtils = () => import('./utils/date-utils');
const loadValidationUtils = () => import('./utils/validation-utils');
```

### Asset Optimization
```typescript
// Image optimization with next/image
const EventTypeImage = ({ src, alt }: { src: string; alt: string }) => (
  <Image
    src={src}
    alt={alt}
    width={300}
    height={200}
    placeholder="blur"
    blurDataURL="data:image/jpeg;base64,..."
    loading="lazy"
    sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
  />
);

// SVG icon optimization
const CalendarIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
    <path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/>
  </svg>
);
```

## Real-time Performance

### Efficient WebSocket Management
```typescript
// Optimized WebSocket connection with connection pooling
class WebSocketManager {
  private connections = new Map<string, WebSocket>();
  private messageQueue = new Map<string, Message[]>();
  
  connect(channel: string): WebSocket {
    if (this.connections.has(channel)) {
      return this.connections.get(channel)!;
    }
    
    const ws = new WebSocket(`${WS_ENDPOINT}/${channel}`);
    
    ws.onopen = () => {
      // Send queued messages
      const queue = this.messageQueue.get(channel) || [];
      queue.forEach(message => ws.send(JSON.stringify(message)));
      this.messageQueue.delete(channel);
    };
    
    ws.onmessage = (event) => {
      const message = JSON.parse(event.data);
      this.handleMessage(channel, message);
    };
    
    this.connections.set(channel, ws);
    return ws;
  }
  
  // Throttle message processing to prevent UI blocking
  private handleMessage = throttle((channel: string, message: Message) => {
    switch (message.type) {
      case 'AVAILABILITY_UPDATE':
        this.updateAvailabilityCache(message.data);
        break;
      case 'BOOKING_CREATED':
        this.updateBookingStore(message.data);
        break;
    }
  }, 100);
}
```

### Performance Monitoring
```typescript
// Real-time performance monitoring
const usePerformanceMonitoring = () => {
  useEffect(() => {
    // Monitor Core Web Vitals
    getCLS(console.log);
    getFID(console.log);
    getFCP(console.log);
    getLCP(console.log);
    getTTFB(console.log);
    
    // Monitor custom metrics
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.entryType === 'measure') {
          console.log(`${entry.name}: ${entry.duration}ms`);
        }
      });
    });
    
    observer.observe({ entryTypes: ['measure'] });
    
    return () => observer.disconnect();
  }, []);
};

// Performance measurement utilities
const measurePerformance = (name: string, fn: () => void) => {
  performance.mark(`${name}-start`);
  fn();
  performance.mark(`${name}-end`);
  performance.measure(name, `${name}-start`, `${name}-end`);
};
```

This comprehensive performance optimization guide ensures the appointment booking module delivers exceptional user experience while maintaining scalability and reliability.
