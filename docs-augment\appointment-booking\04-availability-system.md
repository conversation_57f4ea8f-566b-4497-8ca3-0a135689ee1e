# Availability System - Appointment Booking Module

## Table of Contents
1. [Availability Overview](#availability-overview)
2. [Schedule Data Structures](#schedule-data-structures)
3. [Time Slot Generation](#time-slot-generation)
4. [Availability Calculation](#availability-calculation)
5. [Schedule Management](#schedule-management)
6. [Timezone Handling](#timezone-handling)

## Availability Overview

The Availability System is responsible for determining when appointments can be booked. It combines user-defined schedules, date-specific overrides, existing bookings, and business rules to generate available time slots.

### Core Components
- **Schedules**: Weekly recurring availability patterns
- **Date Overrides**: Specific date modifications (holidays, special hours)
- **Busy Times**: Existing bookings and external calendar conflicts
- **Business Rules**: Minimum notice, maximum advance booking, buffers
- **Time Slot Generation**: Converting availability into bookable slots

## Schedule Data Structures

### Base Schedule Interface
```typescript
interface Schedule {
  id: string;
  name: string;
  timezone: string;
  isDefault: boolean;
  availability: DayAvailability[];
  dateOverrides: DateOverride[];
  createdAt: Date;
  updatedAt: Date;
}

interface DayAvailability {
  day: number; // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
  enabled: boolean;
  timeRanges: TimeRange[];
}

interface TimeRange {
  start: string; // HH:mm format (24-hour)
  end: string;   // HH:mm format (24-hour)
}
```

### Date Override Types
```typescript
interface DateOverride {
  id: string;
  date: string; // YYYY-MM-DD format
  type: OverrideType;
  timeRanges?: TimeRange[];
  reason?: string;
  recurring?: RecurringPattern;
}

enum OverrideType {
  UNAVAILABLE = 'unavailable',    // Block the entire day
  AVAILABLE = 'available',        // Override normal schedule
  CUSTOM_HOURS = 'custom_hours'   // Specific hours for this date
}

interface RecurringPattern {
  frequency: 'weekly' | 'monthly' | 'yearly';
  interval: number; // Every N weeks/months/years
  endDate?: string; // When to stop recurring
  count?: number;   // Number of occurrences
}
```

### Availability Settings
```typescript
interface AvailabilitySettings {
  scheduleId?: string;
  
  // Booking windows
  minimumBookingNotice: number; // Minutes before booking allowed
  maximumBookingNotice?: number; // Days in advance
  
  // Slot configuration
  slotInterval?: number; // Minutes between available slots
  slotDuration: number; // Event duration in minutes
  
  // Buffers
  beforeEventBuffer: number; // Minutes before event
  afterEventBuffer: number;  // Minutes after event
  
  // Limits
  maxBookingsPerSlot?: number; // For group bookings
  maxBookingsPerDay?: number;
  
  // Working hours constraints
  earliestBookingTime?: string; // HH:mm
  latestBookingTime?: string;   // HH:mm
}
```

## Time Slot Generation

### Slot Generation Algorithm
```typescript
interface TimeSlot {
  start: Date;
  end: Date;
  available: boolean;
  reason?: string; // Why unavailable
  attendees?: number; // Current bookings
  maxAttendees?: number; // Capacity
}

interface SlotGenerationParams {
  schedule: Schedule;
  eventType: EventType;
  dateRange: { start: Date; end: Date };
  timezone: string;
  existingBookings: Booking[];
  busyTimes: BusyTime[];
}

function generateTimeSlots(params: SlotGenerationParams): TimeSlot[] {
  const slots: TimeSlot[] = [];
  
  // 1. Iterate through each day in range
  for (const date of getDateRange(params.dateRange)) {
    // 2. Get base availability for day of week
    const dayAvailability = getDayAvailability(date, params.schedule);
    
    // 3. Apply date overrides
    const finalAvailability = applyDateOverrides(date, dayAvailability, params.schedule.dateOverrides);
    
    // 4. Generate slots for each time range
    for (const timeRange of finalAvailability.timeRanges) {
      const daySlots = generateSlotsForRange(date, timeRange, params.eventType);
      slots.push(...daySlots);
    }
  }
  
  // 5. Filter by business rules
  return filterSlotsByBusinessRules(slots, params);
}
```

### Slot Filtering Rules
```typescript
function filterSlotsByBusinessRules(slots: TimeSlot[], params: SlotGenerationParams): TimeSlot[] {
  return slots.filter(slot => {
    // Minimum booking notice
    const now = new Date();
    const minNoticeMs = params.eventType.minimumBookingNotice * 60 * 1000;
    if (slot.start.getTime() - now.getTime() < minNoticeMs) {
      slot.available = false;
      slot.reason = 'Minimum booking notice not met';
      return true; // Keep slot but mark unavailable
    }
    
    // Maximum booking notice
    if (params.eventType.maximumBookingNotice) {
      const maxNoticeMs = params.eventType.maximumBookingNotice * 24 * 60 * 60 * 1000;
      if (slot.start.getTime() - now.getTime() > maxNoticeMs) {
        return false; // Remove slot entirely
      }
    }
    
    // Check conflicts with existing bookings
    const hasConflict = params.existingBookings.some(booking => 
      isTimeSlotConflicting(slot, booking, params.eventType)
    );
    
    if (hasConflict) {
      slot.available = false;
      slot.reason = 'Time slot already booked';
    }
    
    // Check busy times from external calendars
    const isBusy = params.busyTimes.some(busyTime =>
      isTimeSlotConflicting(slot, busyTime, params.eventType)
    );
    
    if (isBusy) {
      slot.available = false;
      slot.reason = 'Conflicting calendar event';
    }
    
    return true;
  });
}
```

## Availability Calculation

### Real-time Availability Check
```typescript
interface AvailabilityQuery {
  eventTypeId: string;
  startDate: string; // YYYY-MM-DD
  endDate: string;   // YYYY-MM-DD
  timezone: string;
}

interface AvailabilityResponse {
  dates: DateAvailability[];
  timezone: string;
  generatedAt: Date;
}

interface DateAvailability {
  date: string; // YYYY-MM-DD
  available: boolean;
  slots: TimeSlot[];
  totalSlots: number;
  availableSlots: number;
}

async function getAvailability(query: AvailabilityQuery): Promise<AvailabilityResponse> {
  // 1. Get event type and schedule
  const eventType = await getEventType(query.eventTypeId);
  const schedule = await getSchedule(eventType.scheduleId);
  
  // 2. Get existing bookings in date range
  const bookings = await getBookingsInRange(query.eventTypeId, query.startDate, query.endDate);
  
  // 3. Get busy times from external calendars
  const busyTimes = await getBusyTimes(eventType.userId, query.startDate, query.endDate);
  
  // 4. Generate time slots
  const slots = generateTimeSlots({
    schedule,
    eventType,
    dateRange: { start: new Date(query.startDate), end: new Date(query.endDate) },
    timezone: query.timezone,
    existingBookings: bookings,
    busyTimes
  });
  
  // 5. Group by date
  const dateGroups = groupSlotsByDate(slots);
  
  // 6. Format response
  return {
    dates: dateGroups.map(group => ({
      date: group.date,
      available: group.slots.some(slot => slot.available),
      slots: group.slots,
      totalSlots: group.slots.length,
      availableSlots: group.slots.filter(slot => slot.available).length
    })),
    timezone: query.timezone,
    generatedAt: new Date()
  };
}
```

### Conflict Detection
```typescript
function isTimeSlotConflicting(
  slot: TimeSlot, 
  booking: Booking | BusyTime, 
  eventType: EventType
): boolean {
  // Calculate effective time range including buffers
  const slotStart = new Date(slot.start.getTime() - (eventType.beforeEventBuffer * 60 * 1000));
  const slotEnd = new Date(slot.end.getTime() + (eventType.afterEventBuffer * 60 * 1000));
  
  const bookingStart = new Date(booking.start);
  const bookingEnd = new Date(booking.end);
  
  // Check for overlap
  return slotStart < bookingEnd && slotEnd > bookingStart;
}
```

## Schedule Management

### Default Schedule Template
```typescript
const DEFAULT_SCHEDULE: Schedule = {
  id: 'default',
  name: 'Default Schedule',
  timezone: 'UTC',
  isDefault: true,
  availability: [
    { day: 0, enabled: false, timeRanges: [] }, // Sunday
    { day: 1, enabled: true, timeRanges: [{ start: '09:00', end: '17:00' }] }, // Monday
    { day: 2, enabled: true, timeRanges: [{ start: '09:00', end: '17:00' }] }, // Tuesday
    { day: 3, enabled: true, timeRanges: [{ start: '09:00', end: '17:00' }] }, // Wednesday
    { day: 4, enabled: true, timeRanges: [{ start: '09:00', end: '17:00' }] }, // Thursday
    { day: 5, enabled: true, timeRanges: [{ start: '09:00', end: '17:00' }] }, // Friday
    { day: 6, enabled: false, timeRanges: [] }, // Saturday
  ],
  dateOverrides: [],
  createdAt: new Date(),
  updatedAt: new Date()
};
```

### Schedule Operations
```typescript
interface ScheduleService {
  // CRUD operations
  create(schedule: Omit<Schedule, 'id' | 'createdAt' | 'updatedAt'>): Promise<Schedule>;
  getById(id: string): Promise<Schedule>;
  update(id: string, updates: Partial<Schedule>): Promise<Schedule>;
  delete(id: string): Promise<void>;
  
  // Bulk operations
  duplicate(sourceId: string, name: string): Promise<Schedule>;
  
  // Date override management
  addDateOverride(scheduleId: string, override: Omit<DateOverride, 'id'>): Promise<DateOverride>;
  updateDateOverride(overrideId: string, updates: Partial<DateOverride>): Promise<DateOverride>;
  deleteDateOverride(overrideId: string): Promise<void>;
  
  // Validation
  validateSchedule(schedule: Schedule): ValidationResult;
  previewAvailability(schedule: Schedule, dateRange: DateRange): Promise<AvailabilityPreview>;
}
```

### Schedule Validation
```typescript
interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

function validateSchedule(schedule: Schedule): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];
  
  // Check for at least one available day
  const hasAvailableDays = schedule.availability.some(day => day.enabled && day.timeRanges.length > 0);
  if (!hasAvailableDays) {
    errors.push({
      field: 'availability',
      message: 'Schedule must have at least one available day',
      code: 'NO_AVAILABLE_DAYS'
    });
  }
  
  // Validate time ranges
  schedule.availability.forEach((day, index) => {
    day.timeRanges.forEach((range, rangeIndex) => {
      if (range.start >= range.end) {
        errors.push({
          field: `availability[${index}].timeRanges[${rangeIndex}]`,
          message: 'Start time must be before end time',
          code: 'INVALID_TIME_RANGE'
        });
      }
    });
    
    // Check for overlapping ranges
    const overlaps = findOverlappingRanges(day.timeRanges);
    if (overlaps.length > 0) {
      warnings.push({
        field: `availability[${index}]`,
        message: 'Overlapping time ranges detected',
        code: 'OVERLAPPING_RANGES'
      });
    }
  });
  
  return {
    valid: errors.length === 0,
    errors,
    warnings
  };
}
```

## Timezone Handling

### Timezone Conversion
```typescript
interface TimezoneService {
  // Convert schedule to user's timezone
  convertScheduleToTimezone(schedule: Schedule, targetTimezone: string): Schedule;
  
  // Convert time slots to user's timezone
  convertSlotsToTimezone(slots: TimeSlot[], targetTimezone: string): TimeSlot[];
  
  // Get timezone offset for date
  getTimezoneOffset(timezone: string, date: Date): number;
  
  // Validate timezone
  isValidTimezone(timezone: string): boolean;
  
  // Get user's detected timezone
  detectUserTimezone(): string;
}

function convertTimeToTimezone(time: string, fromTz: string, toTz: string, date: Date): string {
  // Create datetime in source timezone
  const sourceDateTime = new Date(`${date.toISOString().split('T')[0]}T${time}:00`);
  
  // Convert to target timezone
  const targetDateTime = new Date(sourceDateTime.toLocaleString('en-US', { timeZone: toTz }));
  
  // Return time portion
  return targetDateTime.toTimeString().slice(0, 5);
}
```

### Timezone-Aware Slot Generation
```typescript
function generateSlotsWithTimezone(
  schedule: Schedule,
  eventType: EventType,
  userTimezone: string
): TimeSlot[] {
  // Convert schedule to user's timezone for display
  const userSchedule = convertScheduleToTimezone(schedule, userTimezone);
  
  // Generate slots in user's timezone
  const slots = generateTimeSlots({
    schedule: userSchedule,
    eventType,
    dateRange: getDateRange(),
    timezone: userTimezone,
    existingBookings: [],
    busyTimes: []
  });
  
  return slots;
}
```

### DST Handling
```typescript
function handleDaylightSavingTime(schedule: Schedule, date: Date): Schedule {
  // Check if date falls during DST transition
  const isDSTTransition = isDaylightSavingTransition(date, schedule.timezone);
  
  if (isDSTTransition) {
    // Adjust time ranges for DST transition
    return adjustScheduleForDST(schedule, date);
  }
  
  return schedule;
}
```

## Performance Optimizations

### Caching Strategy
```typescript
interface AvailabilityCache {
  // Cache availability for frequently requested date ranges
  cacheKey: string; // eventTypeId:startDate:endDate:timezone
  data: AvailabilityResponse;
  expiresAt: Date;
}

// Cache availability for 5 minutes
const CACHE_TTL = 5 * 60 * 1000;

async function getCachedAvailability(query: AvailabilityQuery): Promise<AvailabilityResponse | null> {
  const cacheKey = `${query.eventTypeId}:${query.startDate}:${query.endDate}:${query.timezone}`;
  const cached = await cache.get(cacheKey);
  
  if (cached && cached.expiresAt > new Date()) {
    return cached.data;
  }
  
  return null;
}
```

### Batch Processing
```typescript
// Generate availability for multiple event types at once
async function getBatchAvailability(queries: AvailabilityQuery[]): Promise<AvailabilityResponse[]> {
  // Group queries by common parameters
  const grouped = groupQueriesBySchedule(queries);
  
  // Process each group in parallel
  const results = await Promise.all(
    grouped.map(group => processAvailabilityGroup(group))
  );
  
  return results.flat();
}
```

This availability system provides robust, timezone-aware scheduling capabilities while maintaining performance and flexibility for diverse booking scenarios.
