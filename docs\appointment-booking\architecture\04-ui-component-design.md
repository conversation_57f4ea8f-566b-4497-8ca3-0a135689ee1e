# UI Component Design Specifications

## Overview

This document provides detailed UI component specifications for the CRM appointment booking module, defining design patterns, styling guidelines, and component API standards aligned with the existing CRM design system.

## Design System Integration

### 1. Theme Alignment

**Color Palette Integration**:
```typescript
// Extend CRM theme for appointment booking
const appointmentTheme = {
  // Primary colors (aligned with CRM)
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    500: '#3b82f6', // CRM primary
    600: '#2563eb',
    700: '#1d4ed8',
    900: '#1e3a8a'
  },
  
  // Booking-specific colors
  booking: {
    available: '#10b981',      // Green for available slots
    unavailable: '#6b7280',   // Gray for unavailable
    selected: '#3b82f6',      // Primary for selected
    reserved: '#f59e0b',      // Amber for reserved slots
    confirmed: '#059669',     // Emerald for confirmed
    cancelled: '#dc2626'      // Red for cancelled
  },
  
  // Status indicators
  status: {
    pending: '#f59e0b',
    confirmed: '#10b981',
    cancelled: '#ef4444',
    completed: '#6366f1',
    noShow: '#9ca3af'
  }
};
```

**Typography Scale**:
```typescript
// Appointment booking typography
const appointmentTypography = {
  // Headers
  'booking-title': 'text-2xl font-bold text-gray-900',
  'step-title': 'text-lg font-semibold text-gray-800',
  'section-title': 'text-base font-medium text-gray-700',
  
  // Content
  'booking-content': 'text-sm text-gray-600',
  'slot-time': 'text-sm font-medium text-gray-900',
  'slot-duration': 'text-xs text-gray-500',
  
  // Interactive
  'button-text': 'text-sm font-medium',
  'link-text': 'text-sm text-blue-600 hover:text-blue-800',
  
  // Status
  'status-text': 'text-xs font-medium uppercase tracking-wide',
  'error-text': 'text-sm text-red-600',
  'success-text': 'text-sm text-green-600'
};
```

### 2. Component Standards

**Size Scale**:
```typescript
const componentSizes = {
  // Buttons
  button: {
    sm: 'h-8 px-3 text-xs',
    md: 'h-10 px-4 text-sm',
    lg: 'h-12 px-6 text-base'
  },
  
  // Form inputs
  input: {
    sm: 'h-8 px-3 text-xs',
    md: 'h-10 px-3 text-sm', 
    lg: 'h-12 px-4 text-base'
  },
  
  // Cards
  card: {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  },
  
  // Time slots
  timeSlot: {
    compact: 'h-8 px-2 text-xs',
    comfortable: 'h-10 px-3 text-sm',
    spacious: 'h-12 px-4 text-base'
  }
};
```

## Core Component Specifications

### 1. BookingCalendar Component

**Visual Design**:
```typescript
interface CalendarDesignProps {
  // Layout
  gridGap: 'gap-1' | 'gap-2' | 'gap-3';
  cellSize: 'w-8 h-8' | 'w-10 h-10' | 'w-12 h-12';
  
  // Visual indicators
  showWeekNumbers: boolean;
  highlightToday: boolean;
  showAvailabilityDots: boolean;
  
  // Interactive states
  hoverEffects: boolean;
  animateTransitions: boolean;
  
  // Accessibility
  highContrast: boolean;
  focusRingSize: 'ring-2' | 'ring-4';
}

const CalendarStyles = {
  container: cn(
    'bg-white rounded-lg border border-gray-200',
    'shadow-sm overflow-hidden',
    'focus-within:ring-2 focus-within:ring-blue-500'
  ),
  
  header: cn(
    'flex items-center justify-between',
    'px-4 py-3 bg-gray-50 border-b border-gray-200'
  ),
  
  navigation: cn(
    'flex items-center space-x-1'
  ),
  
  navButton: cn(
    'p-2 text-gray-400 hover:text-gray-600',
    'rounded-md hover:bg-gray-100',
    'focus:outline-none focus:ring-2 focus:ring-blue-500'
  ),
  
  monthLabel: cn(
    'text-lg font-semibold text-gray-900',
    'min-w-[120px] text-center'
  ),
  
  weekdayHeaders: cn(
    'grid grid-cols-7 gap-1',
    'px-4 py-2 bg-gray-50',
    'text-xs font-medium text-gray-500 uppercase tracking-wide'
  ),
  
  calendarGrid: cn(
    'grid grid-cols-7 gap-1 p-4'
  ),
  
  dayCell: cn(
    'relative w-10 h-10',
    'flex items-center justify-center',
    'text-sm font-medium',
    'rounded-md cursor-pointer',
    'transition-colors duration-150',
    'focus:outline-none focus:ring-2 focus:ring-blue-500'
  ),
  
  // Day cell variants
  dayAvailable: cn(
    'text-gray-900 bg-white',
    'hover:bg-blue-50 hover:text-blue-700',
    'border border-transparent'
  ),
  
  dayUnavailable: cn(
    'text-gray-300 bg-gray-50',
    'cursor-not-allowed',
    'border border-transparent'
  ),
  
  daySelected: cn(
    'bg-blue-600 text-white',
    'hover:bg-blue-700',
    'border border-blue-600'
  ),
  
  dayToday: cn(
    'border-2 border-blue-600',
    'font-bold'
  ),
  
  availabilityIndicator: cn(
    'absolute bottom-0 right-0',
    'w-2 h-2 rounded-full',
    'bg-green-400'
  )
};
```

**Interaction Design**:
```typescript
const CalendarInteractions = {
  // Keyboard navigation
  keyboardNav: {
    ArrowLeft: 'Navigate to previous day',
    ArrowRight: 'Navigate to next day', 
    ArrowUp: 'Navigate to previous week',
    ArrowDown: 'Navigate to next week',
    Enter: 'Select focused date',
    Space: 'Select focused date',
    Home: 'Go to first day of month',
    End: 'Go to last day of month',
    PageUp: 'Previous month',
    PageDown: 'Next month'
  },
  
  // Touch gestures
  touch: {
    swipeLeft: 'Next month',
    swipeRight: 'Previous month',
    tap: 'Select date',
    longPress: 'Show date details'
  },
  
  // Hover states
  hover: {
    dayCell: 'Highlight with subtle background',
    navButton: 'Show background and increase contrast',
    indicator: 'Show tooltip with availability count'
  }
};
```

### 2. TimeSlotGrid Component

**Layout Design**:
```typescript
const TimeSlotStyles = {
  container: cn(
    'space-y-4'
  ),
  
  timeGroup: cn(
    'space-y-2'
  ),
  
  timeGroupHeader: cn(
    'text-sm font-medium text-gray-700',
    'pb-2 border-b border-gray-100'
  ),
  
  slotGrid: cn(
    'grid gap-2',
    'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6'
  ),
  
  slotButton: cn(
    'relative px-3 py-2',
    'text-sm font-medium',
    'border rounded-md',
    'transition-all duration-150',
    'focus:outline-none focus:ring-2 focus:ring-offset-1 focus:ring-blue-500'
  ),
  
  // Slot variants
  slotAvailable: cn(
    'bg-white border-gray-300 text-gray-900',
    'hover:bg-blue-50 hover:border-blue-300 hover:text-blue-700'
  ),
  
  slotSelected: cn(
    'bg-blue-600 border-blue-600 text-white',
    'hover:bg-blue-700'
  ),
  
  slotUnavailable: cn(
    'bg-gray-50 border-gray-200 text-gray-400',
    'cursor-not-allowed'
  ),
  
  slotReserved: cn(
    'bg-amber-50 border-amber-300 text-amber-700',
    'cursor-not-allowed'
  ),
  
  capacityIndicator: cn(
    'absolute -top-1 -right-1',
    'w-5 h-5 rounded-full',
    'bg-green-500 text-white',
    'text-xs font-bold',
    'flex items-center justify-center'
  ),
  
  loadingSlot: cn(
    'animate-pulse bg-gray-100 border-gray-200'
  )
};
```

**Time Display Formatting**:
```typescript
const TimeFormatting = {
  // 12-hour format
  format12h: (time: string) => {
    return format(new Date(`2000-01-01T${time}`), 'h:mm a');
  },
  
  // 24-hour format
  format24h: (time: string) => {
    return format(new Date(`2000-01-01T${time}`), 'HH:mm');
  },
  
  // Duration display
  formatDuration: (minutes: number) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`;
  },
  
  // Time range display
  formatTimeRange: (startTime: string, duration: number, format: '12h' | '24h') => {
    const start = new Date(`2000-01-01T${startTime}`);
    const end = addMinutes(start, duration);
    
    const formatString = format === '12h' ? 'h:mm a' : 'HH:mm';
    return `${format(start, formatString)} - ${format(end, formatString)}`;
  }
};
```

### 3. BookingForm Component

**Form Layout Design**:
```typescript
const FormStyles = {
  container: cn(
    'space-y-6 bg-white p-6',
    'border border-gray-200 rounded-lg'
  ),
  
  section: cn(
    'space-y-4'
  ),
  
  sectionTitle: cn(
    'text-lg font-medium text-gray-900',
    'pb-2 border-b border-gray-100'
  ),
  
  fieldGroup: cn(
    'grid gap-4',
    'grid-cols-1 sm:grid-cols-2'
  ),
  
  field: cn(
    'space-y-1'
  ),
  
  label: cn(
    'block text-sm font-medium text-gray-700'
  ),
  
  requiredIndicator: cn(
    'text-red-500 ml-1'
  ),
  
  input: cn(
    'block w-full px-3 py-2',
    'border border-gray-300 rounded-md',
    'shadow-sm placeholder-gray-400',
    'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
    'disabled:bg-gray-50 disabled:text-gray-500'
  ),
  
  textarea: cn(
    'block w-full px-3 py-2',
    'border border-gray-300 rounded-md',
    'shadow-sm placeholder-gray-400',
    'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent',
    'resize-vertical min-h-[80px]'
  ),
  
  select: cn(
    'block w-full px-3 py-2',
    'border border-gray-300 rounded-md',
    'shadow-sm bg-white',
    'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent'
  ),
  
  checkbox: cn(
    'h-4 w-4 text-blue-600',
    'border-gray-300 rounded',
    'focus:ring-2 focus:ring-blue-500'
  ),
  
  radio: cn(
    'h-4 w-4 text-blue-600',
    'border-gray-300',
    'focus:ring-2 focus:ring-blue-500'
  ),
  
  helpText: cn(
    'text-sm text-gray-500'
  ),
  
  errorText: cn(
    'text-sm text-red-600'
  ),
  
  successText: cn(
    'text-sm text-green-600'
  )
};
```

**Dynamic Field Rendering**:
```typescript
const DynamicFieldRenderer = {
  text: ({ field, value, onChange, error }: FieldProps) => (
    <div className={FormStyles.field}>
      <label className={FormStyles.label}>
        {field.label}
        {field.required && <span className={FormStyles.requiredIndicator}>*</span>}
      </label>
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={field.placeholder}
        className={cn(
          FormStyles.input,
          error && 'border-red-300 focus:ring-red-500'
        )}
        required={field.required}
      />
      {field.helpText && (
        <p className={FormStyles.helpText}>{field.helpText}</p>
      )}
      {error && (
        <p className={FormStyles.errorText}>{error}</p>
      )}
    </div>
  ),
  
  email: ({ field, value, onChange, error }: FieldProps) => (
    <div className={FormStyles.field}>
      <label className={FormStyles.label}>
        {field.label}
        {field.required && <span className={FormStyles.requiredIndicator}>*</span>}
      </label>
      <input
        type="email"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={field.placeholder}
        className={cn(
          FormStyles.input,
          error && 'border-red-300 focus:ring-red-500'
        )}
        required={field.required}
      />
      {field.helpText && (
        <p className={FormStyles.helpText}>{field.helpText}</p>
      )}
      {error && (
        <p className={FormStyles.errorText}>{error}</p>
      )}
    </div>
  ),
  
  select: ({ field, value, onChange, error }: FieldProps) => (
    <div className={FormStyles.field}>
      <label className={FormStyles.label}>
        {field.label}
        {field.required && <span className={FormStyles.requiredIndicator}>*</span>}
      </label>
      <select
        value={value}
        onChange={(e) => onChange(e.target.value)}
        className={cn(
          FormStyles.select,
          error && 'border-red-300 focus:ring-red-500'
        )}
        required={field.required}
      >
        <option value="">
          {field.placeholder || `Select ${field.label.toLowerCase()}`}
        </option>
        {field.options?.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
      {field.helpText && (
        <p className={FormStyles.helpText}>{field.helpText}</p>
      )}
      {error && (
        <p className={FormStyles.errorText}>{error}</p>
      )}
    </div>
  ),
  
  textarea: ({ field, value, onChange, error }: FieldProps) => (
    <div className={FormStyles.field}>
      <label className={FormStyles.label}>
        {field.label}
        {field.required && <span className={FormStyles.requiredIndicator}>*</span>}
      </label>
      <textarea
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={field.placeholder}
        rows={field.rows || 3}
        className={cn(
          FormStyles.textarea,
          error && 'border-red-300 focus:ring-red-500'
        )}
        required={field.required}
      />
      {field.helpText && (
        <p className={FormStyles.helpText}>{field.helpText}</p>
      )}
      {error && (
        <p className={FormStyles.errorText}>{error}</p>
      )}
    </div>
  )
};
```

### 4. BookingProgress Component

**Progress Indicator Design**:
```typescript
const ProgressStyles = {
  container: cn(
    'flex items-center justify-between',
    'px-4 py-3 bg-gray-50 border-b border-gray-200'
  ),
  
  stepsList: cn(
    'flex items-center space-x-4'
  ),
  
  step: cn(
    'flex items-center space-x-2'
  ),
  
  stepNumber: cn(
    'flex items-center justify-center',
    'w-8 h-8 rounded-full',
    'text-sm font-medium',
    'border-2'
  ),
  
  stepNumberActive: cn(
    'bg-blue-600 border-blue-600 text-white'
  ),
  
  stepNumberCompleted: cn(
    'bg-green-600 border-green-600 text-white'
  ),
  
  stepNumberPending: cn(
    'bg-white border-gray-300 text-gray-500'
  ),
  
  stepLabel: cn(
    'text-sm font-medium'
  ),
  
  stepLabelActive: cn(
    'text-blue-600'
  ),
  
  stepLabelCompleted: cn(
    'text-green-600'
  ),
  
  stepLabelPending: cn(
    'text-gray-500'
  ),
  
  connector: cn(
    'flex-1 h-0.5 mx-4'
  ),
  
  connectorCompleted: cn(
    'bg-green-600'
  ),
  
  connectorPending: cn(
    'bg-gray-300'
  ),
  
  progressBar: cn(
    'w-full bg-gray-200 rounded-full h-2'
  ),
  
  progressFill: cn(
    'bg-blue-600 h-2 rounded-full transition-all duration-300'
  )
};

const ProgressComponent = ({ currentStep, steps, showPercentage }: ProgressProps) => {
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  const progressPercentage = ((currentStepIndex + 1) / steps.length) * 100;
  
  return (
    <div className={ProgressStyles.container}>
      <div className={ProgressStyles.stepsList}>
        {steps.map((step, index) => {
          const isActive = step.id === currentStep;
          const isCompleted = index < currentStepIndex;
          const isPending = index > currentStepIndex;
          
          return (
            <Fragment key={step.id}>
              <div className={ProgressStyles.step}>
                <div className={cn(
                  ProgressStyles.stepNumber,
                  isActive && ProgressStyles.stepNumberActive,
                  isCompleted && ProgressStyles.stepNumberCompleted,
                  isPending && ProgressStyles.stepNumberPending
                )}>
                  {isCompleted ? (
                    <CheckIcon className="w-4 h-4" />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
                <span className={cn(
                  ProgressStyles.stepLabel,
                  isActive && ProgressStyles.stepLabelActive,
                  isCompleted && ProgressStyles.stepLabelCompleted,
                  isPending && ProgressStyles.stepLabelPending
                )}>
                  {step.label}
                </span>
              </div>
              
              {index < steps.length - 1 && (
                <div className={cn(
                  ProgressStyles.connector,
                  index < currentStepIndex 
                    ? ProgressStyles.connectorCompleted 
                    : ProgressStyles.connectorPending
                )} />
              )}
            </Fragment>
          );
        })}
      </div>
      
      {showPercentage && (
        <div className="text-sm text-gray-500">
          {Math.round(progressPercentage)}%
        </div>
      )}
    </div>
  );
};
```

### 5. StatusBadge Component

**Status Indicator Design**:
```typescript
const StatusBadgeStyles = {
  base: cn(
    'inline-flex items-center px-2.5 py-0.5',
    'rounded-full text-xs font-medium',
    'ring-1 ring-inset'
  ),
  
  // Booking status variants
  pending: cn(
    'bg-amber-50 text-amber-700 ring-amber-600/20'
  ),
  
  confirmed: cn(
    'bg-green-50 text-green-700 ring-green-600/20'
  ),
  
  cancelled: cn(
    'bg-red-50 text-red-700 ring-red-600/20'
  ),
  
  completed: cn(
    'bg-blue-50 text-blue-700 ring-blue-600/20'
  ),
  
  noShow: cn(
    'bg-gray-50 text-gray-700 ring-gray-600/20'
  ),
  
  // Size variants
  sm: 'px-2 py-0.5 text-xs',
  md: 'px-2.5 py-0.5 text-xs',
  lg: 'px-3 py-1 text-sm'
};

const StatusBadge = ({ 
  status, 
  size = 'md', 
  showIcon = true, 
  className 
}: StatusBadgeProps) => {
  const statusConfig = {
    pending: { icon: ClockIcon, label: 'Pending' },
    confirmed: { icon: CheckCircleIcon, label: 'Confirmed' },
    cancelled: { icon: XCircleIcon, label: 'Cancelled' },
    completed: { icon: CheckIcon, label: 'Completed' },
    noShow: { icon: ExclamationTriangleIcon, label: 'No Show' }
  };
  
  const config = statusConfig[status];
  const IconComponent = config.icon;
  
  return (
    <span className={cn(
      StatusBadgeStyles.base,
      StatusBadgeStyles[status],
      StatusBadgeStyles[size],
      className
    )}>
      {showIcon && <IconComponent className="w-3 h-3 mr-1" />}
      {config.label}
    </span>
  );
};
```

## Responsive Design Patterns

### 1. Mobile-First Approach

**Breakpoint Strategy**:
```typescript
const ResponsiveBreakpoints = {
  // Mobile (default)
  mobile: {
    calendar: 'grid-cols-7 gap-1',
    timeSlots: 'grid-cols-2 gap-2',
    form: 'grid-cols-1 gap-4',
    progress: 'hidden' // Show simplified version
  },
  
  // Tablet (sm: 640px+)
  tablet: {
    calendar: 'sm:gap-2',
    timeSlots: 'sm:grid-cols-3 sm:gap-3',
    form: 'sm:grid-cols-2 sm:gap-6',
    progress: 'sm:block'
  },
  
  // Desktop (md: 768px+)
  desktop: {
    calendar: 'md:gap-3',
    timeSlots: 'md:grid-cols-4 md:gap-4',
    form: 'md:grid-cols-2 md:gap-8',
    progress: 'md:flex'
  },
  
  // Large (lg: 1024px+)
  large: {
    calendar: 'lg:gap-4',
    timeSlots: 'lg:grid-cols-6 lg:gap-4',
    form: 'lg:grid-cols-3 lg:gap-8',
    progress: 'lg:justify-center'
  }
};
```

**Adaptive Component Behavior**:
```typescript
const useResponsiveBooking = () => {
  const { isMobile, isTablet, isDesktop } = useBreakpoint();
  
  return {
    // Calendar configuration
    calendarConfig: {
      showWeekNumbers: !isMobile,
      cellSize: isMobile ? 'w-8 h-8' : 'w-10 h-10',
      navigation: isMobile ? 'compact' : 'full'
    },
    
    // Time slot configuration
    timeSlotConfig: {
      columns: isMobile ? 2 : isTablet ? 3 : 6,
      size: isMobile ? 'compact' : 'comfortable',
      showDuration: !isMobile
    },
    
    // Form configuration
    formConfig: {
      layout: isMobile ? 'stacked' : 'grid',
      columns: isMobile ? 1 : 2,
      labelPosition: isMobile ? 'top' : 'left'
    },
    
    // Progress configuration
    progressConfig: {
      variant: isMobile ? 'minimal' : 'full',
      showLabels: !isMobile,
      showPercentage: isDesktop
    }
  };
};
```

### 2. Touch Optimization

**Touch Targets**:
```typescript
const TouchTargets = {
  // Minimum touch target sizes
  minimum: 'min-h-[44px] min-w-[44px]', // iOS/Android guidelines
  
  // Calendar day cells
  calendarDay: cn(
    'w-10 h-10', // Base size
    'sm:w-12 sm:h-12', // Larger on tablet
    'touch-manipulation' // Optimize touch response
  ),
  
  // Time slot buttons
  timeSlot: cn(
    'min-h-[44px] px-3', // Ensure minimum height
    'touch-manipulation',
    'active:scale-95' // Visual feedback
  ),
  
  // Form inputs
  formInput: cn(
    'min-h-[44px] px-3',
    'touch-manipulation'
  ),
  
  // Navigation buttons
  navButton: cn(
    'w-12 h-12', // Square touch target
    'flex items-center justify-center',
    'touch-manipulation'
  )
};
```

## Animation and Transitions

### 1. Micro-Interactions

**Animation Configuration**:
```typescript
const Animations = {
  // Component entrance
  slideIn: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.2, ease: 'easeOut' }
  },
  
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: 0.3 }
  },
  
  // State changes
  buttonPress: {
    whileTap: { scale: 0.95 },
    transition: { duration: 0.1 }
  },
  
  slotSelection: {
    initial: { scale: 1 },
    animate: { scale: 1.02 },
    transition: { duration: 0.15 }
  },
  
  // Loading states
  skeleton: {
    animate: {
      backgroundColor: ['#f3f4f6', '#e5e7eb', '#f3f4f6'],
    },
    transition: {
      duration: 1.5,
      repeat: Infinity,
      ease: 'easeInOut'
    }
  },
  
  // Progress updates
  progressBar: {
    animate: { width: '60%' },
    transition: { duration: 0.5, ease: 'easeOut' }
  }
};
```

### 2. Loading States

**Skeleton Components**:
```typescript
const SkeletonComponents = {
  CalendarSkeleton: () => (
    <div className="bg-white rounded-lg border border-gray-200 p-4">
      <div className="flex items-center justify-between mb-4">
        <Skeleton className="h-6 w-32" />
        <div className="flex space-x-2">
          <Skeleton className="h-8 w-8 rounded" />
          <Skeleton className="h-8 w-8 rounded" />
        </div>
      </div>
      <div className="grid grid-cols-7 gap-1">
        {Array.from({ length: 35 }, (_, i) => (
          <Skeleton key={i} className="h-10 w-10 rounded" />
        ))}
      </div>
    </div>
  ),
  
  TimeSlotSkeleton: () => (
    <div className="space-y-4">
      {Array.from({ length: 3 }, (_, i) => (
        <div key={i} className="space-y-2">
          <Skeleton className="h-4 w-16" />
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2">
            {Array.from({ length: 8 }, (_, j) => (
              <Skeleton key={j} className="h-10 rounded-md" />
            ))}
          </div>
        </div>
      ))}
    </div>
  ),
  
  FormSkeleton: () => (
    <div className="space-y-6">
      {Array.from({ length: 4 }, (_, i) => (
        <div key={i} className="space-y-2">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-10 w-full rounded-md" />
        </div>
      ))}
    </div>
  )
};
```

## Accessibility Standards

### 1. ARIA Implementation

**Semantic Structure**:
```typescript
const AccessibilityProps = {
  calendar: {
    role: 'grid',
    'aria-label': 'Calendar for selecting appointment date',
    'aria-readonly': false,
    'aria-multiselectable': false
  },
  
  calendarDay: (date: string, isSelected: boolean, isAvailable: boolean) => ({
    role: 'gridcell',
    'aria-label': `${formatDate(date, 'EEEE, MMMM do, yyyy')}${
      isAvailable ? ', available' : ', unavailable'
    }${isSelected ? ', selected' : ''}`,
    'aria-selected': isSelected,
    'aria-disabled': !isAvailable,
    tabIndex: isSelected ? 0 : -1
  }),
  
  timeSlot: (time: string, isSelected: boolean, isAvailable: boolean) => ({
    role: 'button',
    'aria-label': `${formatTime(time)}${
      isAvailable ? ', available' : ', unavailable'
    }${isSelected ? ', selected' : ''}`,
    'aria-pressed': isSelected,
    'aria-disabled': !isAvailable,
    tabIndex: isAvailable ? 0 : -1
  }),
  
  form: {
    role: 'form',
    'aria-label': 'Appointment booking form',
    noValidate: true
  },
  
  formField: (field: BookingField, error?: string) => ({
    'aria-required': field.required,
    'aria-invalid': !!error,
    'aria-describedby': error ? `${field.name}-error` : undefined
  })
};
```

### 2. Keyboard Navigation

**Navigation Patterns**:
```typescript
const KeyboardNavigation = {
  calendar: {
    ArrowLeft: 'Move to previous day',
    ArrowRight: 'Move to next day',
    ArrowUp: 'Move to previous week',
    ArrowDown: 'Move to next week',
    Home: 'Move to first day of month',
    End: 'Move to last day of month',
    PageUp: 'Move to previous month',
    PageDown: 'Move to next month',
    Enter: 'Select focused date',
    Space: 'Select focused date'
  },
  
  timeSlots: {
    ArrowLeft: 'Move to previous slot',
    ArrowRight: 'Move to next slot',
    ArrowUp: 'Move to slot above',
    ArrowDown: 'Move to slot below',
    Home: 'Move to first slot',
    End: 'Move to last slot',
    Enter: 'Select focused slot',
    Space: 'Select focused slot'
  },
  
  form: {
    Tab: 'Move to next field',
    'Shift+Tab': 'Move to previous field',
    Enter: 'Submit form (if valid)',
    Escape: 'Cancel form'
  }
};

// Keyboard navigation hook
const useKeyboardNavigation = (
  type: 'calendar' | 'timeSlots' | 'form',
  items: any[],
  onSelect: (item: any) => void
) => {
  const [focusedIndex, setFocusedIndex] = useState(0);
  
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    const actions = KeyboardNavigation[type];
    
    switch (event.key) {
      case 'ArrowLeft':
        event.preventDefault();
        setFocusedIndex(Math.max(0, focusedIndex - 1));
        break;
      case 'ArrowRight':
        event.preventDefault();
        setFocusedIndex(Math.min(items.length - 1, focusedIndex + 1));
        break;
      case 'Enter':
      case ' ':
        event.preventDefault();
        onSelect(items[focusedIndex]);
        break;
    }
  }, [focusedIndex, items, onSelect]);
  
  return {
    focusedIndex,
    setFocusedIndex,
    handleKeyDown
  };
};
```

## Performance Considerations

### 1. Component Optimization

**Memoization Strategy**:
```typescript
// Memoized calendar component
const CalendarGrid = memo(({ 
  days, 
  selectedDate, 
  onDateSelect 
}: CalendarGridProps) => {
  return (
    <div className="grid grid-cols-7 gap-1">
      {days.map((day) => (
        <CalendarDay
          key={day.date}
          day={day}
          isSelected={day.date === selectedDate}
          onSelect={onDateSelect}
        />
      ))}
    </div>
  );
}, (prevProps, nextProps) => {
  return (
    prevProps.selectedDate === nextProps.selectedDate &&
    isEqual(prevProps.days, nextProps.days)
  );
});

// Memoized time slot
const TimeSlotButton = memo(({ 
  slot, 
  isSelected, 
  onSelect 
}: TimeSlotButtonProps) => {
  const handleClick = useCallback(() => {
    onSelect(slot.time);
  }, [slot.time, onSelect]);
  
  return (
    <button
      onClick={handleClick}
      className={cn(
        TimeSlotStyles.slotButton,
        isSelected ? TimeSlotStyles.slotSelected : TimeSlotStyles.slotAvailable
      )}
      aria-pressed={isSelected}
    >
      {formatTime(slot.time)}
    </button>
  );
});
```

### 2. CSS Optimization

**Critical CSS Strategy**:
```typescript
// Critical styles for above-the-fold content
const CriticalStyles = `
  .booking-calendar {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.25rem;
  }
  
  .calendar-day {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: background-color 0.15s ease;
  }
  
  .time-slot {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.15s ease;
  }
`;

// Lazy load non-critical styles
const LazyStyles = lazy(() => import('./booking-animations.css'));
```

## Testing Standards

### 1. Visual Testing

**Component Visual Tests**:
```typescript
// Storybook stories for visual testing
export const CalendarStories = {
  Default: {
    render: () => (
      <BookingCalendar
        eventType={mockEventType}
        availability={mockAvailability}
        onDateSelect={action('date-selected')}
      />
    )
  },
  
  WithLimitedAvailability: {
    render: () => (
      <BookingCalendar
        eventType={mockEventType}
        availability={limitedAvailability}
        onDateSelect={action('date-selected')}
      />
    )
  },
  
  Loading: {
    render: () => (
      <BookingCalendar
        eventType={mockEventType}
        availability={{}}
        isLoading={true}
        onDateSelect={action('date-selected')}
      />
    )
  },
  
  MobileView: {
    render: () => (
      <div style={{ width: '375px' }}>
        <BookingCalendar
          eventType={mockEventType}
          availability={mockAvailability}
          onDateSelect={action('date-selected')}
        />
      </div>
    )
  }
};
```

### 2. Accessibility Testing

**A11y Test Suite**:
```typescript
describe('BookingCalendar Accessibility', () => {
  it('should have proper ARIA attributes', () => {
    render(<BookingCalendar {...defaultProps} />);
    
    const calendar = screen.getByRole('grid');
    expect(calendar).toHaveAttribute('aria-label', 'Calendar for selecting appointment date');
    
    const days = screen.getAllByRole('gridcell');
    expect(days[0]).toHaveAttribute('aria-label');
    expect(days[0]).toHaveAttribute('tabIndex');
  });
  
  it('should support keyboard navigation', () => {
    render(<BookingCalendar {...defaultProps} />);
    
    const firstDay = screen.getAllByRole('gridcell')[0];
    firstDay.focus();
    
    fireEvent.keyDown(firstDay, { key: 'ArrowRight' });
    
    const secondDay = screen.getAllByRole('gridcell')[1];
    expect(secondDay).toHaveFocus();
  });
  
  it('should announce changes to screen readers', () => {
    render(<BookingCalendar {...defaultProps} />);
    
    const liveRegion = screen.getByRole('status', { hidden: true });
    expect(liveRegion).toBeInTheDocument();
    
    const day = screen.getByRole('gridcell', { name: /march 15/i });
    fireEvent.click(day);
    
    expect(liveRegion).toHaveTextContent(/selected march 15/i);
  });
});
```

## Implementation Guidelines

### 1. Component Development Checklist

- [ ] **TypeScript**: Strict typing with proper interfaces
- [ ] **Accessibility**: ARIA attributes and keyboard navigation
- [ ] **Responsive**: Mobile-first responsive design
- [ ] **Performance**: Memoization and optimization
- [ ] **Testing**: Unit tests and visual tests
- [ ] **Documentation**: Storybook stories and API docs
- [ ] **Error Handling**: Graceful error states
- [ ] **Loading States**: Skeleton components
- [ ] **Animation**: Smooth transitions
- [ ] **Theme Integration**: Consistent with CRM design

### 2. Quality Gates

- **Performance**: Lighthouse score > 90
- **Accessibility**: WCAG 2.1 AA compliance
- **Browser Support**: Chrome, Firefox, Safari, Edge
- **Mobile Support**: iOS Safari, Chrome Mobile
- **Bundle Size**: < 50KB per component chunk
- **Test Coverage**: > 80% code coverage

## Related Documents

- [01-crm-integration-design.md](./01-crm-integration-design.md) - Overall architecture
- [02-component-architecture.md](./02-component-architecture.md) - Component structure
- [03-state-management-design.md](./03-state-management-design.md) - State management
- [../implementation/03-ui-implementation-guide.md](../implementation/03-ui-implementation-guide.md) - Implementation details
