# Component Architecture - Appointment Booking Module

## Table of Contents
1. [Component Hierarchy](#component-hierarchy)
2. [Core Booking Components](#core-booking-components)
3. [Event Type Components](#event-type-components)
4. [Availability Components](#availability-components)
5. [Calendar Components](#calendar-components)
6. [Shared Components](#shared-components)

## Component Hierarchy

```
AppointmentBookingModule
├── BookingInterface/
│   ├── Booker (Main booking component)
│   ├── BookEventForm
│   ├── AvailableTimeSlots
│   ├── DatePicker
│   └── BookingConfirmation
├── EventTypeManagement/
│   ├── EventTypesList
│   ├── CreateEventType
│   ├── EditEventType
│   └── EventTypeSettings
├── AvailabilityManagement/
│   ├── ScheduleEditor
│   ├── AvailabilityOverrides
│   └── TimezoneSelector
├── CalendarViews/
│   ├── BookingListView
│   ├── BookingGridView
│   ├── FullCalendarView
│   └── WeeklyCalendarView
└── PublicBookingPages/
    ├── PublicBooker
    ├── EventTypePublicPage
    └── BookingSuccessPage
```

## Core Booking Components

### 1. Booker Component
**Purpose**: Main booking interface that orchestrates the entire booking flow
**Location**: `src/app/(main)/appointments/components/booking/booker/Booker.tsx`

```typescript
interface BookerProps {
  username: string;
  eventSlug: string;
  rescheduleUid?: string;
  bookingUid?: string;
  layout?: BookerLayout;
  isTeamEvent?: boolean;
  org?: string;
  entity?: BookerEntity;
}

interface BookerState {
  state: 'loading' | 'selecting_date' | 'selecting_time' | 'booking' | 'success';
  selectedDate: string | null;
  selectedTimeslot: string | null;
  month: string;
  layout: BookerLayout;
  timezone: string;
}
```

**Key Features**:
- State management for booking flow
- Layout switching (month, week, column views)
- Timezone handling
- Responsive design
- Accessibility support

**Dependencies**:
- BookEventForm
- AvailableTimeSlots
- DatePicker
- EventMeta
- Header

### 2. BookEventForm Component
**Purpose**: Handles booking form submission and validation
**Location**: `src/app/(main)/appointments/components/booking/event-form/BookEventForm.tsx`

```typescript
interface BookEventFormProps {
  eventType: EventType;
  selectedTimeslot: string;
  onSubmit: (data: BookingFormData) => void;
  onCancel: () => void;
  errors: BookingErrors;
  isLoading: boolean;
  rescheduleUid?: string;
}

interface BookingFormData {
  name: string;
  email: string;
  phone?: string;
  notes?: string;
  guests?: string[];
  customFields: Record<string, any>;
  timezone: string;
}
```

**Key Features**:
- Dynamic form fields based on event type
- Real-time validation
- Guest management
- Custom field support
- Timezone selection

### 3. AvailableTimeSlots Component
**Purpose**: Displays and manages time slot selection
**Location**: `src/app/(main)/appointments/components/booking/time-slots/AvailableTimeSlots.tsx`

```typescript
interface AvailableTimeSlotsProps {
  date: string;
  eventType: EventType;
  availability: TimeSlot[];
  onTimeSelect: (time: string) => void;
  selectedTime?: string;
  timezone: string;
  isLoading: boolean;
}

interface TimeSlot {
  time: string;
  available: boolean;
  attendees?: number;
  maxAttendees?: number;
  price?: number;
}
```

**Key Features**:
- Time slot grid display
- Availability indicators
- Seat management for group events
- Price display
- Loading states

### 4. DatePicker Component
**Purpose**: Calendar interface for date selection
**Location**: `src/app/(main)/appointments/components/booking/calendar/DatePicker.tsx`

```typescript
interface DatePickerProps {
  selectedDate?: string;
  onDateSelect: (date: string) => void;
  availability: AvailabilityData;
  minDate?: Date;
  maxDate?: Date;
  timezone: string;
  eventType: EventType;
}

interface AvailabilityData {
  [date: string]: {
    available: boolean;
    slots: number;
  };
}
```

**Key Features**:
- Month/week/day views
- Availability indicators
- Keyboard navigation
- Mobile-responsive
- Timezone-aware

## Event Type Components

### 1. EventTypesList Component
**Purpose**: Displays and manages all event types
**Location**: `src/app/(main)/appointments/components/event-types/list/EventTypesList.tsx`

```typescript
interface EventTypesListProps {
  eventTypes: EventType[];
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
  onDuplicate: (id: string) => void;
  onToggleActive: (id: string, active: boolean) => void;
}

interface EventType {
  id: string;
  title: string;
  slug: string;
  description?: string;
  length: number;
  price?: number;
  currency?: string;
  locations: EventLocation[];
  active: boolean;
  bookingCount: number;
  lastBooking?: Date;
}
```

**Key Features**:
- Grid/list view toggle
- Bulk operations
- Search and filtering
- Sorting options
- Quick actions

### 2. CreateEventType Component
**Purpose**: Form for creating new event types
**Location**: `src/app/(main)/appointments/components/event-types/create/CreateEventType.tsx`

```typescript
interface CreateEventTypeProps {
  onSuccess: (eventType: EventType) => void;
  onCancel: () => void;
  defaultValues?: Partial<EventType>;
}

interface EventTypeFormData {
  title: string;
  slug: string;
  description?: string;
  length: number;
  locations: EventLocation[];
  price?: number;
  currency?: string;
  bookingFields: BookingField[];
  availability: AvailabilitySettings;
  limits: BookingLimits;
  metadata: EventTypeMetadata;
}
```

**Key Features**:
- Multi-step form wizard
- Real-time slug generation
- Location management
- Pricing configuration
- Advanced settings

### 3. EditEventType Component
**Purpose**: Comprehensive event type editing interface
**Location**: `src/app/(main)/appointments/components/event-types/edit/EditEventType.tsx`

```typescript
interface EditEventTypeProps {
  eventTypeId: string;
  onSave: (data: EventTypeFormData) => void;
  onCancel: () => void;
}
```

**Key Features**:
- Tabbed interface
- Live preview
- Change tracking
- Validation
- Auto-save

## Availability Components

### 1. ScheduleEditor Component
**Purpose**: Interface for setting up availability schedules
**Location**: `src/app/(main)/appointments/components/availability/schedule/ScheduleEditor.tsx`

```typescript
interface ScheduleEditorProps {
  schedule: Schedule;
  onChange: (schedule: Schedule) => void;
  timezone: string;
}

interface Schedule {
  name: string;
  timezone: string;
  availability: DayAvailability[];
  dateOverrides: DateOverride[];
}

interface DayAvailability {
  day: number; // 0-6 (Sunday-Saturday)
  enabled: boolean;
  timeRanges: TimeRange[];
}

interface TimeRange {
  start: string; // HH:mm format
  end: string;   // HH:mm format
}
```

**Key Features**:
- Visual time range editor
- Drag-and-drop interface
- Copy/paste between days
- Bulk operations
- Timezone conversion

### 2. AvailabilityOverrides Component
**Purpose**: Manages date-specific availability changes
**Location**: `src/app/(main)/appointments/components/availability/overrides/AvailabilityOverrides.tsx`

```typescript
interface AvailabilityOverridesProps {
  overrides: DateOverride[];
  onChange: (overrides: DateOverride[]) => void;
  timezone: string;
}

interface DateOverride {
  date: string;
  type: 'available' | 'unavailable' | 'custom';
  timeRanges?: TimeRange[];
  reason?: string;
}
```

**Key Features**:
- Calendar-based override selection
- Bulk date operations
- Recurring overrides
- Holiday management
- Conflict detection

## Calendar Components

### 1. BookingListView Component
**Purpose**: List-based display of bookings
**Location**: `src/app/(main)/appointments/components/views/list-view/BookingListView.tsx`

```typescript
interface BookingListViewProps {
  bookings: Booking[];
  filters: BookingFilters;
  onFilterChange: (filters: BookingFilters) => void;
  onBookingAction: (action: BookingAction, bookingId: string) => void;
}

interface Booking {
  id: string;
  title: string;
  attendees: Attendee[];
  startTime: Date;
  endTime: Date;
  status: BookingStatus;
  eventType: EventType;
  location?: string;
  notes?: string;
}
```

**Key Features**:
- Sortable columns
- Advanced filtering
- Bulk actions
- Export functionality
- Pagination

### 2. FullCalendarView Component
**Purpose**: Traditional calendar grid display
**Location**: `src/app/(main)/appointments/components/views/calendar-view/FullCalendarView.tsx`

```typescript
interface FullCalendarViewProps {
  bookings: Booking[];
  view: 'month' | 'week' | 'day';
  onViewChange: (view: CalendarView) => void;
  onDateChange: (date: Date) => void;
  onBookingClick: (booking: Booking) => void;
}
```

**Key Features**:
- Multiple view modes
- Drag-and-drop rescheduling
- Event overlays
- Navigation controls
- Responsive design

## Shared Components

### 1. BookingCard Component
**Purpose**: Reusable booking display card
**Location**: `src/app/(main)/appointments/components/shared/BookingCard.tsx`

```typescript
interface BookingCardProps {
  booking: Booking;
  variant: 'compact' | 'detailed' | 'minimal';
  actions?: BookingAction[];
  onClick?: () => void;
}
```

### 2. TimeSlotButton Component
**Purpose**: Individual time slot selection button
**Location**: `src/app/(main)/appointments/components/shared/TimeSlotButton.tsx`

```typescript
interface TimeSlotButtonProps {
  time: string;
  available: boolean;
  selected?: boolean;
  attendees?: number;
  maxAttendees?: number;
  price?: number;
  onClick: () => void;
}
```

### 3. EventTypeCard Component
**Purpose**: Event type display and management card
**Location**: `src/app/(main)/appointments/components/shared/EventTypeCard.tsx`

```typescript
interface EventTypeCardProps {
  eventType: EventType;
  variant: 'grid' | 'list' | 'public';
  actions?: EventTypeAction[];
  onClick?: () => void;
}
```

## Component Communication Patterns

### 1. Props Down, Events Up
- Parent components pass data via props
- Child components emit events for state changes
- Clear data flow and responsibility separation

### 2. Context for Shared State
- BookingContext for booking flow state
- EventTypeContext for event type operations
- AvailabilityContext for schedule management

### 3. Custom Hooks for Logic
- useBookingFlow for booking state management
- useAvailability for availability calculations
- useEventTypes for event type operations

### 4. Store Integration
- Zustand stores for global state
- Local state for component-specific data
- Optimistic updates for better UX

This component architecture provides a scalable, maintainable foundation for the appointment booking module while ensuring consistency with the existing CRM structure.
