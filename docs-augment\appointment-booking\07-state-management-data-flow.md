# State Management & Data Flow - Appointment Booking Module

## Table of Contents
1. [State Architecture Overview](#state-architecture-overview)
2. [Zustand Store Structure](#zustand-store-structure)
3. [Data Flow Patterns](#data-flow-patterns)
4. [State Synchronization](#state-synchronization)
5. [Performance Optimizations](#performance-optimizations)
6. [Error State Management](#error-state-management)

## State Architecture Overview

The appointment booking module uses a layered state management approach combining Zustand for global state, React's built-in state for component-specific data, and custom hooks for business logic.

### State Layers
```typescript
interface StateArchitecture {
  // Global Application State (Zustand)
  globalState: {
    bookingStore: BookingStore;
    eventTypeStore: EventTypeStore;
    availabilityStore: AvailabilityStore;
    uiStore: UIStore;
  };
  
  // Component State (React useState/useReducer)
  componentState: {
    formState: FormState;
    viewState: ViewState;
    interactionState: InteractionState;
  };
  
  // Derived State (Custom Hooks)
  derivedState: {
    computedAvailability: ComputedAvailability;
    filteredBookings: FilteredBookings;
    validationState: ValidationState;
  };
}
```

### State Flow Principles
- **Single Source of Truth**: Each piece of data has one authoritative source
- **Unidirectional Data Flow**: Data flows down, events flow up
- **Immutable Updates**: State changes create new objects
- **Optimistic Updates**: UI updates immediately, syncs with server
- **Error Boundaries**: Graceful error handling and recovery

## Zustand Store Structure

### Booking Store
```typescript
interface BookingStore {
  // State
  bookings: Booking[];
  selectedBooking: Booking | null;
  bookingFilters: BookingFilters;
  bookingSorting: SortConfiguration;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setBookings: (bookings: Booking[]) => void;
  addBooking: (booking: Booking) => void;
  updateBooking: (id: string, updates: Partial<Booking>) => void;
  deleteBooking: (id: string) => void;
  setSelectedBooking: (booking: Booking | null) => void;
  setFilters: (filters: BookingFilters) => void;
  setSorting: (sorting: SortConfiguration) => void;
  
  // Async Actions
  fetchBookings: (filters?: BookingFilters) => Promise<void>;
  createBooking: (data: CreateBookingData) => Promise<Booking>;
  rescheduleBooking: (id: string, newDateTime: DateTime) => Promise<void>;
  cancelBooking: (id: string, reason?: string) => Promise<void>;
  
  // Computed State
  getFilteredBookings: () => Booking[];
  getBookingsByDate: (date: string) => Booking[];
  getUpcomingBookings: () => Booking[];
  getBookingStats: () => BookingStats;
}

const useBookingStore = create<BookingStore>((set, get) => ({
  // Initial state
  bookings: [],
  selectedBooking: null,
  bookingFilters: DEFAULT_BOOKING_FILTERS,
  bookingSorting: DEFAULT_BOOKING_SORTING,
  isLoading: false,
  error: null,
  
  // Actions
  setBookings: (bookings) => set({ bookings }),
  
  addBooking: (booking) => set((state) => ({
    bookings: [...state.bookings, booking]
  })),
  
  updateBooking: (id, updates) => set((state) => ({
    bookings: state.bookings.map(booking =>
      booking.id === id ? { ...booking, ...updates } : booking
    )
  })),
  
  deleteBooking: (id) => set((state) => ({
    bookings: state.bookings.filter(booking => booking.id !== id)
  })),
  
  // Async actions
  fetchBookings: async (filters) => {
    set({ isLoading: true, error: null });
    try {
      const bookings = await bookingService.getBookings(filters);
      set({ bookings, isLoading: false });
    } catch (error) {
      set({ error: error.message, isLoading: false });
    }
  },
  
  createBooking: async (data) => {
    set({ isLoading: true, error: null });
    try {
      const booking = await bookingService.createBooking(data);
      set((state) => ({
        bookings: [...state.bookings, booking],
        isLoading: false
      }));
      return booking;
    } catch (error) {
      set({ error: error.message, isLoading: false });
      throw error;
    }
  },
  
  // Computed state
  getFilteredBookings: () => {
    const { bookings, bookingFilters } = get();
    return filterBookings(bookings, bookingFilters);
  },
  
  getBookingsByDate: (date) => {
    const { bookings } = get();
    return bookings.filter(booking => 
      format(booking.startTime, 'yyyy-MM-dd') === date
    );
  }
}));
```

### Event Type Store
```typescript
interface EventTypeStore {
  // State
  eventTypes: EventType[];
  selectedEventType: EventType | null;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setEventTypes: (eventTypes: EventType[]) => void;
  addEventType: (eventType: EventType) => void;
  updateEventType: (id: string, updates: Partial<EventType>) => void;
  deleteEventType: (id: string) => void;
  setSelectedEventType: (eventType: EventType | null) => void;
  
  // Async Actions
  fetchEventTypes: () => Promise<void>;
  createEventType: (data: CreateEventTypeData) => Promise<EventType>;
  duplicateEventType: (id: string, name: string) => Promise<EventType>;
  
  // Computed State
  getActiveEventTypes: () => EventType[];
  getEventTypeBySlug: (slug: string) => EventType | undefined;
  getEventTypeStats: () => EventTypeStats;
}
```

### Availability Store
```typescript
interface AvailabilityStore {
  // State
  schedules: Schedule[];
  selectedSchedule: Schedule | null;
  availability: AvailabilityData;
  timeSlots: TimeSlot[];
  isLoading: boolean;
  error: string | null;
  
  // Actions
  setSchedules: (schedules: Schedule[]) => void;
  setSelectedSchedule: (schedule: Schedule | null) => void;
  setAvailability: (availability: AvailabilityData) => void;
  setTimeSlots: (slots: TimeSlot[]) => void;
  
  // Async Actions
  fetchSchedules: () => Promise<void>;
  fetchAvailability: (params: AvailabilityQuery) => Promise<void>;
  updateSchedule: (id: string, updates: Partial<Schedule>) => Promise<void>;
  
  // Computed State
  getAvailableSlots: (date: string) => TimeSlot[];
  getScheduleByDate: (date: string) => DayAvailability | null;
  isDateAvailable: (date: string) => boolean;
}
```

### UI Store
```typescript
interface UIStore {
  // View State
  currentView: CalendarViewType;
  currentDate: Date;
  selectedDate: string | null;
  selectedTimeSlot: string | null;
  
  // Modal State
  modals: {
    createBooking: boolean;
    editBooking: boolean;
    createEventType: boolean;
    editEventType: boolean;
    confirmCancel: boolean;
  };
  
  // Loading States
  loadingStates: {
    bookings: boolean;
    eventTypes: boolean;
    availability: boolean;
    creating: boolean;
    updating: boolean;
    deleting: boolean;
  };
  
  // Notification State
  notifications: Notification[];
  
  // Actions
  setCurrentView: (view: CalendarViewType) => void;
  setCurrentDate: (date: Date) => void;
  setSelectedDate: (date: string | null) => void;
  setSelectedTimeSlot: (slot: string | null) => void;
  openModal: (modal: keyof UIStore['modals']) => void;
  closeModal: (modal: keyof UIStore['modals']) => void;
  setLoadingState: (key: keyof UIStore['loadingStates'], loading: boolean) => void;
  addNotification: (notification: Notification) => void;
  removeNotification: (id: string) => void;
}
```

## Data Flow Patterns

### Advanced Booking Creation Flow
```typescript
interface BookingCreationFlow {
  // 1. User selects time slot with reservation
  onTimeSlotSelect: async (slot: TimeSlot) => {
    try {
      // Reserve slot temporarily
      await reservationStore.reserveSlot(slot);

      uiStore.setSelectedTimeSlot(slot.time);
      uiStore.openModal('createBooking');

      // Auto-release reservation after timeout
      setTimeout(() => {
        reservationStore.releaseSlot(slot.id);
      }, 10 * 60 * 1000); // 10 minutes
    } catch (error) {
      uiStore.addNotification({
        type: 'error',
        message: 'Slot is no longer available'
      });
    }
  };

  // 2. User fills booking form with optimistic updates
  onFormSubmit: async (formData: BookingFormData) => {
    uiStore.setLoadingState('creating', true);

    try {
      // Create optimistic booking
      const optimisticBooking = createOptimisticBooking(formData);
      bookingStore.addBooking(optimisticBooking);

      // Confirm slot reservation
      await reservationStore.confirmReservation(formData.slotId);

      // Server request with retry logic
      const booking = await createBookingWithRetry(formData);

      // Replace optimistic with real data
      bookingStore.updateBooking(optimisticBooking.id, booking);

      // Success feedback with actions
      uiStore.addNotification({
        type: 'success',
        message: 'Booking created successfully',
        actions: [
          { label: 'Add to Calendar', action: () => addToCalendar(booking) },
          { label: 'View Details', action: () => viewBooking(booking.id) }
        ]
      });

      uiStore.closeModal('createBooking');

      // Trigger integrations
      await triggerBookingIntegrations(booking);

    } catch (error) {
      // Rollback optimistic update
      bookingStore.deleteBooking(optimisticBooking.id);

      // Release reservation
      await reservationStore.releaseSlot(formData.slotId);

      // Enhanced error handling
      handleBookingError(error, formData);
    } finally {
      uiStore.setLoadingState('creating', false);
    }
  };
}

// Optimistic updates with rollback
const useOptimisticBooking = () => {
  const createBookingOptimistic = async (data: CreateBookingData) => {
    const optimisticId = generateTempId();
    const optimisticBooking: Booking = {
      ...data,
      id: optimisticId,
      status: 'pending',
      createdAt: new Date(),
      updatedAt: new Date(),
      confirmationNumber: `TEMP-${optimisticId.slice(-6)}`
    };

    // Add to store immediately for instant UI feedback
    bookingStore.addBooking(optimisticBooking);

    try {
      // Actual API call
      const actualBooking = await bookingService.createBooking(data);

      // Replace optimistic with real booking
      bookingStore.updateBooking(optimisticId, {
        ...actualBooking,
        id: actualBooking.id // Use real ID
      });

      // Remove the optimistic entry
      bookingStore.deleteBooking(optimisticId);

      return actualBooking;
    } catch (error) {
      // Rollback: remove optimistic booking
      bookingStore.deleteBooking(optimisticId);

      // Re-throw for error handling
      throw error;
    }
  };

  return { createBookingOptimistic };
};

// Enhanced error handling
const handleBookingError = (error: BookingError, formData: BookingFormData) => {
  switch (error.type) {
    case 'SLOT_UNAVAILABLE':
      uiStore.addNotification({
        type: 'warning',
        message: 'This time slot is no longer available',
        actions: [
          {
            label: 'Find Similar Times',
            action: () => suggestAlternativeSlots(formData.selectedDate)
          }
        ]
      });
      break;

    case 'VALIDATION_ERROR':
      uiStore.addNotification({
        type: 'error',
        message: 'Please check your booking information',
        details: error.validationErrors
      });
      break;

    case 'NETWORK_ERROR':
      uiStore.addNotification({
        type: 'error',
        message: 'Connection problem. Your booking will be retried automatically.',
        actions: [
          { label: 'Retry Now', action: () => retryBooking(formData) }
        ]
      });
      break;

    default:
      uiStore.addNotification({
        type: 'error',
        message: 'Something went wrong. Please try again.'
      });
  }
};
```
```

### Availability Calculation Flow
```typescript
interface AvailabilityFlow {
  // 1. Date range changes
  onDateRangeChange: async (startDate: Date, endDate: Date) => {
    const query: AvailabilityQuery = {
      eventTypeId: selectedEventType.id,
      startDate: format(startDate, 'yyyy-MM-dd'),
      endDate: format(endDate, 'yyyy-MM-dd'),
      timezone: userTimezone
    };
    
    // Check cache first
    const cached = availabilityCache.get(query);
    if (cached && !cached.isExpired()) {
      availabilityStore.setAvailability(cached.data);
      return;
    }
    
    // Fetch from server
    availabilityStore.setLoadingState('availability', true);
    
    try {
      await availabilityStore.fetchAvailability(query);
      
      // Cache result
      availabilityCache.set(query, availabilityStore.availability);
    } catch (error) {
      uiStore.addNotification({
        type: 'error',
        message: 'Failed to load availability'
      });
    } finally {
      availabilityStore.setLoadingState('availability', false);
    }
  };
}
```

### Filter & Search Flow
```typescript
interface FilterFlow {
  // Debounced search
  onSearchChange: useDebouncedCallback((searchTerm: string) => {
    const filters = {
      ...bookingStore.bookingFilters,
      search: searchTerm
    };
    
    bookingStore.setFilters(filters);
    
    // Update URL
    updateURLParams({ search: searchTerm });
  }, 300);
  
  // Filter changes
  onFilterChange: (filterKey: string, value: any) => {
    const filters = {
      ...bookingStore.bookingFilters,
      [filterKey]: value
    };
    
    bookingStore.setFilters(filters);
    
    // Persist to localStorage
    localStorage.setItem('bookingFilters', JSON.stringify(filters));
    
    // Update URL
    updateURLParams(filters);
  };
}
```

## State Synchronization

### Enhanced Real-time Updates
```typescript
interface RealTimeSync {
  // WebSocket connection for live updates with reconnection
  setupWebSocket: () => {
    let ws: WebSocket;
    let reconnectAttempts = 0;
    const maxReconnectAttempts = 5;

    const connect = () => {
      ws = new WebSocket(WS_ENDPOINT);

      ws.onopen = () => {
        console.log('WebSocket connected');
        reconnectAttempts = 0;

        // Subscribe to relevant channels
        ws.send(JSON.stringify({
          type: 'SUBSCRIBE',
          channels: ['bookings', 'availability', 'reservations']
        }));
      };

      ws.onmessage = (event) => {
        const update = JSON.parse(event.data);
        handleRealTimeUpdate(update);
      };

      ws.onclose = () => {
        if (reconnectAttempts < maxReconnectAttempts) {
          setTimeout(() => {
            reconnectAttempts++;
            connect();
          }, Math.pow(2, reconnectAttempts) * 1000); // Exponential backoff
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
      };
    };

    connect();
    return ws;
  };

  // Enhanced update handling
  handleRealTimeUpdate: (update: RealTimeUpdate) => {
    switch (update.type) {
      case 'BOOKING_CREATED':
        bookingStore.addBooking(update.booking);

        // Show notification if it affects current user
        if (update.booking.conflictsWithCurrent) {
          uiStore.addNotification({
            type: 'warning',
            message: 'A new booking may affect your current selection'
          });
        }
        break;

      case 'SLOT_RESERVED':
        // Update availability immediately
        availabilityStore.markSlotReserved(update.slotId, update.reservedBy);

        // Refresh time slots if viewing same date
        if (uiStore.selectedDate === update.date) {
          availabilityStore.refreshTimeSlots(update.date);
        }
        break;

      case 'SLOT_RELEASED':
        availabilityStore.markSlotAvailable(update.slotId);
        break;

      case 'BOOKING_CANCELLED':
        bookingStore.updateBooking(update.bookingId, {
          status: 'cancelled',
          cancelledAt: new Date(update.timestamp)
        });

        // Refresh availability for that date
        availabilityStore.refreshAvailability(update.date);
        break;

      case 'AVAILABILITY_CHANGED':
        // Invalidate and refetch availability
        availabilityStore.invalidateCache();
        availabilityStore.fetchAvailability(update.query);
        break;

      case 'CONFLICT_DETECTED':
        handleBookingConflict(update.conflict);
        break;
    }
  };

  // Intelligent periodic sync
  setupPeriodicSync: () => {
    const syncInterval = setInterval(() => {
      const now = new Date();
      const currentHour = now.getHours();

      // More frequent sync during business hours
      const isBusinessHours = currentHour >= 9 && currentHour <= 17;
      const syncFrequency = isBusinessHours ? 30000 : 120000; // 30s vs 2min

      // Sync today's bookings
      const today = format(now, 'yyyy-MM-dd');
      const todayBookings = bookingStore.getBookingsByDate(today);

      if (todayBookings.length > 0) {
        bookingStore.fetchBookings({
          dateRange: { start: now, end: now },
          silent: true // Don't show loading states
        });
      }

      // Sync current availability if user is actively booking
      if (uiStore.currentView === 'booking' && uiStore.selectedDate) {
        availabilityStore.refreshTimeSlots(uiStore.selectedDate);
      }
    }, 60000);

    return () => clearInterval(syncInterval);
  };
}
```

### Offline Support
```typescript
interface OfflineSync {
  // Queue actions when offline
  queueAction: (action: OfflineAction) => {
    const queue = getOfflineQueue();
    queue.push(action);
    localStorage.setItem('offlineQueue', JSON.stringify(queue));
  };
  
  // Process queue when back online
  processOfflineQueue: async () => {
    const queue = getOfflineQueue();
    
    for (const action of queue) {
      try {
        await processAction(action);
      } catch (error) {
        // Handle conflicts or errors
        handleOfflineConflict(action, error);
      }
    }
    
    // Clear processed queue
    localStorage.removeItem('offlineQueue');
  };
  
  // Detect online/offline status
  setupOfflineDetection: () => {
    window.addEventListener('online', processOfflineQueue);
    window.addEventListener('offline', () => {
      uiStore.addNotification({
        type: 'warning',
        message: 'You are offline. Changes will sync when reconnected.'
      });
    });
  };
}
```

## Performance Optimizations

### Memoization Strategies
```typescript
interface MemoizationConfig {
  // Selector memoization
  selectors: {
    filteredBookings: (bookings: Booking[], filters: BookingFilters) => Booking[];
    availableSlots: (availability: AvailabilityData, date: string) => TimeSlot[];
    bookingStats: (bookings: Booking[]) => BookingStats;
  };
  
  // Component memoization
  components: {
    BookingCard: React.memo;
    TimeSlotButton: React.memo;
    CalendarDay: React.memo;
  };
  
  // Hook memoization
  hooks: {
    useFilteredBookings: () => useMemo(() => getFilteredBookings(), [bookings, filters]);
    useAvailableSlots: () => useMemo(() => getAvailableSlots(), [availability, date]);
  };
}

// Zustand selector optimization
const useFilteredBookings = () => useBookingStore(
  useCallback(
    (state) => filterBookings(state.bookings, state.bookingFilters),
    []
  ),
  shallow
);
```

### Lazy Loading
```typescript
interface LazyLoadingStrategy {
  // Component lazy loading
  components: {
    BookingModal: lazy(() => import('./BookingModal'));
    EventTypeEditor: lazy(() => import('./EventTypeEditor'));
    CalendarView: lazy(() => import('./CalendarView'));
  };
  
  // Data lazy loading
  data: {
    loadBookingsOnDemand: (dateRange: DateRange) => Promise<Booking[]>;
    loadAvailabilityOnScroll: (nextMonth: Date) => Promise<AvailabilityData>;
    preloadUpcomingBookings: () => Promise<void>;
  };
}
```

### Virtual Scrolling
```typescript
interface VirtualScrollConfig {
  itemHeight: number;
  containerHeight: number;
  overscan: number;
  
  // Calculate visible items
  getVisibleRange: (scrollTop: number) => {
    start: number;
    end: number;
  };
  
  // Render only visible items
  renderItems: (items: any[], visibleRange: VisibleRange) => ReactNode[];
}
```

## Error State Management

### Error Boundaries
```typescript
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
}

class BookingErrorBoundary extends Component<Props, ErrorBoundaryState> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    };
  }
  
  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return { hasError: true, error };
  }
  
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({ errorInfo });
    
    // Log error to monitoring service
    errorLogger.log(error, errorInfo);
  }
  
  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1
    }));
  };
  
  render() {
    if (this.state.hasError) {
      return (
        <ErrorFallback
          error={this.state.error}
          onRetry={this.handleRetry}
          retryCount={this.state.retryCount}
        />
      );
    }
    
    return this.props.children;
  }
}
```

### Error Recovery Strategies
```typescript
interface ErrorRecovery {
  // Automatic retry with exponential backoff
  retryWithBackoff: async (fn: () => Promise<any>, maxRetries = 3) => {
    for (let i = 0; i < maxRetries; i++) {
      try {
        return await fn();
      } catch (error) {
        if (i === maxRetries - 1) throw error;
        
        const delay = Math.pow(2, i) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  };
  
  // Graceful degradation
  fallbackToCache: (key: string) => {
    const cached = cache.get(key);
    if (cached) {
      uiStore.addNotification({
        type: 'warning',
        message: 'Showing cached data due to connection issues'
      });
      return cached;
    }
    return null;
  };
  
  // User-initiated recovery
  recoverFromError: (error: Error) => {
    switch (error.type) {
      case 'NETWORK_ERROR':
        return retryLastAction();
      case 'VALIDATION_ERROR':
        return showValidationErrors(error.details);
      case 'PERMISSION_ERROR':
        return redirectToLogin();
      default:
        return showGenericError();
    }
  };
}
```

This comprehensive state management system ensures reliable, performant, and maintainable data flow throughout the appointment booking module while providing excellent user experience even in error scenarios.
