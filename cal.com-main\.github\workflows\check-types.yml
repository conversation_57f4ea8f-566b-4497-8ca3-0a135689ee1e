name: Check types
on:
  workflow_call:
env:
  NODE_OPTIONS: --max-old-space-size=4096
permissions:
  contents: read
jobs:
  check-types:
    runs-on: buildjet-4vcpu-ubuntu-2204
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/dangerous-git-checkout
      - uses: ./.github/actions/yarn-install
      - name: Show info
        run: node -e "console.log(require('v8').getHeapStatistics())"
      - name: Configure TSC problem matcher
        run: |
          echo "::remove-matcher owner=tsc::"
          echo "::add-matcher::.github/matchers/tsc-absolute.json"
      - run: yarn type-check:ci
