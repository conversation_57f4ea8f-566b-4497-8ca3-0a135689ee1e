# Appointment Booking Module - Core Architecture Overview

## Table of Contents
1. [System Overview](#system-overview)
2. [Tech Stack Integration](#tech-stack-integration)
3. [Module Structure](#module-structure)
4. [Core Principles](#core-principles)
5. [Integration with Existing CRM](#integration-with-existing-crm)

## System Overview

The Appointment Booking Module is a comprehensive scheduling system inspired by cal.com, designed to integrate seamlessly with the existing CRM structure. This module enables users to create event types, manage availability, and allow visitors to book appointments through a user-friendly interface.

### Key Features
- **Event Type Management**: Create and configure different types of bookable events
- **Availability Scheduling**: Set up complex availability patterns and time slots
- **Booking Interface**: Visitor-facing booking pages with real-time availability
- **Calendar Views**: Multiple viewing modes (list, grid, full calendar)
- **Booking Management**: Comprehensive booking lifecycle management

### Architecture Philosophy
- **Component-First Design**: Modular, reusable components following existing CRM patterns
- **State-Driven**: Centralized state management using Zustand
- **Type-Safe**: Full TypeScript implementation with strict typing
- **Accessibility-First**: WCAG compliant components using shadcn/ui
- **Performance-Optimized**: Server components and efficient data fetching

## Tech Stack Integration

### Frontend Framework
- **Next.js 15.1.5**: App Router with Server Components
- **React 19**: Concurrent features and enhanced hydration
- **TypeScript 5.7.3**: Strict mode with zero 'any' types

### Styling & UI
- **Tailwind CSS 4.0.1**: Container queries and optimized builds
- **shadcn/ui**: Accessibility-first component library
- **CSS Variables**: Dynamic theming support

### State Management
- **Zustand 5.0.5**: Lightweight state management
- **React Hook Form**: Form state and validation
- **Valibot 0.32.1**: Client-side validation schemas

### Data Layer (Frontend Only)
- **Mock Data Services**: Simulated API responses
- **Local Storage**: Persistent state for demo purposes
- **TypeScript Interfaces**: Strongly typed data contracts

## Module Structure

```
src/app/(main)/appointments/
├── components/
│   ├── booking/
│   │   ├── booker/                 # Main booking interface
│   │   ├── event-form/             # Booking form components
│   │   ├── calendar/               # Calendar components
│   │   └── time-slots/             # Time slot selection
│   ├── event-types/
│   │   ├── create/                 # Event type creation
│   │   ├── edit/                   # Event type editing
│   │   └── list/                   # Event type management
│   ├── availability/
│   │   ├── schedule/               # Availability scheduling
│   │   ├── overrides/              # Date-specific overrides
│   │   └── time-zones/             # Timezone handling
│   └── views/
│       ├── calendar-view/          # Full calendar display
│       ├── list-view/              # Booking list view
│       └── grid-view/              # Grid layout view
├── hooks/
│   ├── use-booking-store.ts        # Booking state management
│   ├── use-availability.ts         # Availability logic
│   └── use-event-types.ts          # Event type operations
├── lib/
│   ├── types/                      # TypeScript definitions
│   ├── utils/                      # Utility functions
│   ├── services/                   # Mock data services
│   └── stores/                     # Zustand stores
├── data/
│   ├── mock-bookings.ts            # Sample booking data
│   ├── mock-event-types.ts         # Sample event types
│   └── mock-availability.ts        # Sample availability
└── [event-slug]/                   # Public booking pages
    ├── page.tsx                    # Booking page
    └── components/                 # Public-facing components
```

## Core Principles

### 1. Separation of Concerns
- **Presentation Layer**: React components focused on UI rendering
- **Business Logic**: Custom hooks for complex operations
- **Data Layer**: Services and stores for data management
- **Utilities**: Pure functions for common operations

### 2. Consistency with Existing CRM
- **File Structure**: Follows established CRM patterns
- **Component Naming**: Consistent with existing conventions
- **Styling Patterns**: Reuses existing design tokens
- **State Management**: Extends current Zustand patterns

### 3. Scalability Considerations
- **Modular Components**: Easy to extend and modify
- **Type Safety**: Prevents runtime errors
- **Performance**: Optimized rendering and data fetching
- **Maintainability**: Clear separation and documentation

### 4. User Experience Focus
- **Intuitive Navigation**: Clear user flows
- **Responsive Design**: Mobile-first approach
- **Accessibility**: Keyboard navigation and screen readers
- **Loading States**: Smooth user feedback

## Integration with Existing CRM

### Shared Components
The booking module leverages existing CRM components:
- **UI Components**: Button, Card, Dialog, Form elements
- **Layout Components**: Page headers, sidebars, navigation
- **Data Components**: Tables, filters, pagination
- **Utility Components**: Loading states, error boundaries

### State Management Integration
- **Extends Current Stores**: Builds upon existing Zustand patterns
- **Shared Types**: Reuses common type definitions
- **Service Layer**: Follows established service patterns

### Styling Consistency
- **Design System**: Uses existing design tokens
- **Component Variants**: Consistent with CRM styling
- **Responsive Patterns**: Matches current breakpoints
- **Theme Support**: Integrates with existing theme system

### Navigation Integration
- **Sidebar Navigation**: Adds booking-related menu items
- **Breadcrumbs**: Consistent navigation patterns
- **Page Routing**: Follows established URL structures
- **Deep Linking**: Supports bookmarkable URLs

## Next Steps

1. **Component Architecture**: Detailed component specifications
2. **Event Type Management**: Configuration and data structures
3. **Availability System**: Scheduling and time slot management
4. **Booking Flow**: Complete user journey mapping
5. **Calendar Views**: Display mode implementations
6. **State Management**: Store structure and data flow
7. **Mock Data**: Comprehensive sample data sets
8. **UI Components**: Reusable component catalog
9. **Integration Points**: CRM connection specifications

This architecture provides a solid foundation for building a comprehensive appointment booking system that seamlessly integrates with the existing CRM while maintaining consistency, performance, and user experience standards.
