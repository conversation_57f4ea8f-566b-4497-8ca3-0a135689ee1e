---
name: Bug report
about: Report any issues with the platform
title: ""
labels: ["🐛 bug"]
assignees: ""
---

Found a bug? Please fill out the sections below. 👍

### Issue Summary

A summary of the issue. This needs to be a clear detailed-rich summary.

### Steps to Reproduce

1. (for example) Went to ...
2. Clicked on...
3. ...

Any other relevant information. For example, why do you consider this a bug and what did you expect to happen instead?

### Actual Results

- What's happening right now that is different from what is expected

### Expected Results

- This is an ideal result that the system should get after the tests are performed

### Technical details

- Browser version, screen recording, console logs, network requests: You can make a recording with [Bird Eats Bug](https://birdeatsbug.com/).
- Node.js version
- Anything else that you think could be an issue.

### Evidence

- How was this tested? This is quite mandatory in terms of bugs. Providing evidence of your testing with screenshots or/and videos is an amazing way to prove the bug and a troubleshooting chance to find the solution.
