# CRM Appointment Booking Architecture Design

## Executive Summary

This document outlines the architectural design for integrating an appointment booking module into the existing CRM system. The design leverages Cal.com's proven patterns while maintaining consistency with the CRM's existing architecture and technology stack.

## Technology Stack Alignment

### Current CRM Stack
- **Frontend**: Next.js 15.1.5 (App Router)
- **UI Framework**: React 19
- **Styling**: Tailwind CSS 4.0.1
- **Component Library**: shadcn/ui
- **TypeScript**: 5.7.3 (Strict mode)
- **Validation**: Valibot 0.32.1
- **State Management**: Zustand 5.0.5

### Appointment Booking Integration
- **Component Base**: Adapted from Cal.com components
- **State Management**: Extended Zustand stores
- **Validation**: Valibot for form validation
- **Styling**: Tailwind CSS with CRM theme integration
- **Type Safety**: Strict TypeScript throughout

## Architecture Principles

### 1. Consistency with Existing CRM
- Follow established CRM component patterns
- Integrate with existing Zustand stores
- Maintain consistent styling and UX
- Reuse common utilities and types

### 2. Modular Design
- Self-contained booking module
- Pluggable architecture for easy integration
- Reusable components across CRM features
- Clear separation of concerns

### 3. Scalability
- Support for future multi-tenant requirements
- Extensible event type system
- Flexible booking field configuration
- Performance optimized for growth

### 4. Developer Experience
- Type-safe throughout
- Comprehensive documentation
- Clear component API boundaries
- Easy testing and debugging

## Module Structure

### Recommended File Structure
```
src/app/(main)/appointments/
├── components/
│   ├── booking/
│   │   ├── booking-calendar.tsx
│   │   ├── time-slot-selector.tsx
│   │   ├── booking-form.tsx
│   │   ├── booking-confirmation.tsx
│   │   └── index.ts
│   ├── event-types/
│   │   ├── event-type-card.tsx
│   │   ├── event-type-list.tsx
│   │   ├── create-event-type.tsx
│   │   └── index.ts
│   ├── availability/
│   │   ├── availability-settings.tsx
│   │   ├── schedule-editor.tsx
│   │   ├── time-range-picker.tsx
│   │   └── index.ts
│   ├── shared/
│   │   ├── appointment-card.tsx
│   │   ├── calendar-views.tsx
│   │   ├── booking-status-badge.tsx
│   │   └── index.ts
│   └── index.ts
├── hooks/
│   ├── use-booking-flow.ts
│   ├── use-availability.ts
│   ├── use-event-types.ts
│   └── use-calendar-integration.ts
├── lib/
│   ├── stores/
│   │   ├── booking-store.ts
│   │   ├── event-types-store.ts
│   │   └── availability-store.ts
│   ├── types/
│   │   ├── booking.ts
│   │   ├── event-types.ts
│   │   ├── availability.ts
│   │   └── index.ts
│   ├── utils/
│   │   ├── date-time.ts
│   │   ├── booking-helpers.ts
│   │   ├── validation.ts
│   │   └── calendar-utils.ts
│   └── services/
│       ├── booking-service.ts
│       ├── availability-service.ts
│       └── calendar-service.ts
├── data/
│   ├── mock-event-types.ts
│   ├── mock-bookings.ts
│   ├── mock-availability.ts
│   └── index.ts
├── (booking-flow)/
│   ├── [eventType]/
│   │   ├── components/
│   │   │   ├── booking-widget.tsx
│   │   │   └── booking-layout.tsx
│   │   └── page.tsx
│   └── layout.tsx
├── event-types/
│   ├── components/
│   ├── new/
│   │   └── page.tsx
│   ├── [id]/
│   │   ├── edit/
│   │   │   └── page.tsx
│   │   └── page.tsx
│   └── page.tsx
├── availability/
│   ├── components/
│   └── page.tsx
├── bookings/
│   ├── components/
│   ├── [id]/
│   │   └── page.tsx
│   └── page.tsx
├── calendar/
│   ├── components/
│   └── page.tsx
└── page.tsx
```

## Core Architecture Components

### 1. Booking Flow Architecture

**State-Driven Flow Management**:
```typescript
// Booking flow states
enum BookingFlowState {
  SELECTING_EVENT_TYPE = "selecting_event_type",
  SELECTING_DATE = "selecting_date", 
  SELECTING_TIME = "selecting_time",
  FILLING_DETAILS = "filling_details",
  CONFIRMING = "confirming",
  SUCCESS = "success",
  ERROR = "error"
}

// Flow progression
const flowTransitions = {
  [BookingFlowState.SELECTING_EVENT_TYPE]: BookingFlowState.SELECTING_DATE,
  [BookingFlowState.SELECTING_DATE]: BookingFlowState.SELECTING_TIME,
  [BookingFlowState.SELECTING_TIME]: BookingFlowState.FILLING_DETAILS,
  [BookingFlowState.FILLING_DETAILS]: BookingFlowState.CONFIRMING,
  [BookingFlowState.CONFIRMING]: BookingFlowState.SUCCESS,
  // Error state can transition from any state
};
```

### 2. Component Layer Architecture

**Hierarchical Component Structure**:
```
AppointmentBookingModule
├── BookingFlowProvider (Context + Store)
├── BookingLayout (Layout wrapper)
├── EventTypeSelector (Step 1)
├── DateSelector (Step 2)
│   ├── CalendarView
│   └── AvailabilityIndicator
├── TimeSlotSelector (Step 3)
│   ├── TimeSlotGrid
│   └── TimezoneSelector
├── BookingForm (Step 4)
│   ├── AttendeeForm
│   ├── DynamicFields
│   └── BookingNotes
├── BookingConfirmation (Step 5)
│   ├── BookingSummary
│   ├── CalendarIntegration
│   └── ConfirmationActions
└── BookingSuccess (Final)
    ├── BookingDetails
    ├── CalendarLinks
    └── NextSteps
```

### 3. Data Layer Architecture

**Store Integration Pattern**:
```typescript
// Extend existing CRM store pattern
interface AppointmentBookingStore {
  // Booking flow state
  currentStep: BookingFlowState;
  
  // Selection state
  selectedEventType: EventType | null;
  selectedDate: string | null;
  selectedTime: string | null;
  
  // Form state  
  attendeeDetails: AttendeeInfo;
  bookingNotes: string;
  
  // Booking context
  currentBooking: Booking | null;
  isRescheduling: boolean;
  
  // Actions
  setCurrentStep: (step: BookingFlowState) => void;
  selectEventType: (eventType: EventType) => void;
  selectDateTime: (date: string, time: string) => void;
  updateAttendeeDetails: (details: AttendeeInfo) => void;
  createBooking: () => Promise<void>;
  
  // Integration with existing CRM stores
  syncWithContacts: () => void;
  syncWithTasks: () => void;
}
```

## Data Model Design

### 1. Core Entities

**Event Type Model**:
```typescript
interface EventType {
  id: string;
  title: string;
  description?: string;
  duration: number; // minutes
  slug: string;
  
  // Scheduling
  availability: AvailabilitySchedule;
  bufferTime: number; // minutes before/after
  
  // Booking settings
  requiresConfirmation: boolean;
  maxBookingsPerDay?: number;
  bookingFields: BookingField[];
  
  // Integration
  crmUserId: string;
  isActive: boolean;
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
}
```

**Booking Model**:
```typescript
interface Booking {
  id: string;
  eventTypeId: string;
  
  // Scheduling
  startTime: Date;
  endTime: Date;
  timezone: string;
  
  // Attendee info
  attendeeName: string;
  attendeeEmail: string;
  attendeePhone?: string;
  additionalInfo: Record<string, any>;
  
  // Status
  status: BookingStatus;
  confirmationCode: string;
  
  // CRM Integration
  contactId?: string; // Link to CRM contact
  taskId?: string;    // Auto-generated task
  
  // Metadata
  createdAt: Date;
  updatedAt: Date;
  cancelledAt?: Date;
}

enum BookingStatus {
  PENDING = "pending",
  CONFIRMED = "confirmed", 
  CANCELLED = "cancelled",
  COMPLETED = "completed",
  NO_SHOW = "no_show"
}
```

**Availability Model**:
```typescript
interface AvailabilitySchedule {
  id: string;
  eventTypeId: string;
  
  // Weekly schedule
  weeklyHours: WeeklyAvailability;
  
  // Exceptions
  dateOverrides: DateOverride[];
  blockedDates: string[]; // YYYY-MM-DD format
  
  // Settings
  timezone: string;
  minimumNotice: number; // hours
  maximumNotice?: number; // days
  
  // Buffer settings
  bufferTimeBefore: number; // minutes
  bufferTimeAfter: number; // minutes
}

interface WeeklyAvailability {
  monday: TimeRange[];
  tuesday: TimeRange[];
  wednesday: TimeRange[];
  thursday: TimeRange[];
  friday: TimeRange[];
  saturday: TimeRange[];
  sunday: TimeRange[];
}

interface TimeRange {
  start: string; // HH:mm format
  end: string;   // HH:mm format
}

interface DateOverride {
  date: string; // YYYY-MM-DD
  timeRanges: TimeRange[];
  isBlocked: boolean;
}
```

### 2. Integration Models

**CRM Contact Integration**:
```typescript
interface ContactBookingData {
  contactId: string;
  bookings: Booking[];
  upcomingAppointments: Booking[];
  appointmentHistory: Booking[];
  preferredEventTypes: string[];
  lastBookingDate?: Date;
  totalBookings: number;
  noShowCount: number;
}
```

**Task Integration**:
```typescript
interface AppointmentTask {
  id: string;
  bookingId: string;
  contactId?: string;
  
  // Task details
  title: string;
  description: string;
  dueDate: Date;
  
  // Appointment specific
  appointmentType: "preparation" | "follow_up" | "reminder";
  automationRules: TaskAutomationRule[];
  
  // CRM integration
  taskStatus: TaskStatus;
  assignedUserId: string;
}
```

## Integration Patterns

### 1. CRM Store Integration

**Appointment Store Extension**:
```typescript
// Extend existing CRM patterns
const useAppointmentStore = create<AppointmentStore>((set, get) => ({
  // Booking state
  bookings: [],
  eventTypes: [],
  currentBooking: null,
  
  // Integration with existing stores
  addBookingToContact: (bookingId: string, contactId: string) => {
    const { updateContact } = useContactStore.getState();
    const booking = get().bookings.find(b => b.id === bookingId);
    
    if (booking) {
      updateContact(contactId, {
        lastAppointment: booking.startTime,
        totalAppointments: (contact.totalAppointments || 0) + 1
      });
    }
  },
  
  createTaskForBooking: (booking: Booking) => {
    const { addTask } = useTaskStore.getState();
    
    const task = {
      id: generateId(),
      title: `Appointment: ${booking.attendeeName}`,
      description: `Upcoming appointment on ${formatDate(booking.startTime)}`,
      dueDate: booking.startTime,
      contactId: booking.contactId,
      type: "appointment",
      status: "pending"
    };
    
    addTask(task);
  }
}));
```

### 2. Service Layer Integration

**Booking Service Pattern**:
```typescript
class BookingService {
  // Core booking operations
  async createBooking(bookingData: CreateBookingRequest): Promise<Booking> {
    // 1. Validate availability
    const isAvailable = await this.checkAvailability(
      bookingData.eventTypeId,
      bookingData.startTime
    );
    
    if (!isAvailable) {
      throw new Error("Time slot no longer available");
    }
    
    // 2. Create booking
    const booking = await this.persistBooking(bookingData);
    
    // 3. CRM integrations
    await this.syncWithCRM(booking);
    
    // 4. Notifications
    await this.sendConfirmations(booking);
    
    return booking;
  }
  
  private async syncWithCRM(booking: Booking): Promise<void> {
    // Create or update contact
    if (booking.contactId) {
      await this.updateContactBookingHistory(booking);
    } else {
      const contact = await this.createContactFromBooking(booking);
      booking.contactId = contact.id;
    }
    
    // Create follow-up task
    await this.createFollowUpTask(booking);
    
    // Update CRM metrics
    await this.updateCRMMetrics(booking);
  }
}
```

### 3. Component Integration Pattern

**CRM Component Wrapper**:
```typescript
// Wrapper to integrate booking components with CRM
export const CRMAppointmentBooking = ({
  contactId,
  prefilledData,
  onBookingComplete,
  ...props
}: CRMAppointmentBookingProps) => {
  const { contact } = useContactStore();
  const { addTask } = useTaskStore();
  
  // Pre-fill form with contact data
  const initialData = useMemo(() => ({
    name: contact?.name || "",
    email: contact?.email || "",
    phone: contact?.phone || "",
    ...prefilledData
  }), [contact, prefilledData]);
  
  const handleBookingSuccess = (booking: Booking) => {
    // Create CRM task
    addTask({
      title: `Follow up: ${booking.attendeeName}`,
      dueDate: addDays(booking.startTime, 1),
      contactId: contactId,
      type: "follow_up"
    });
    
    // Callback to parent component
    onBookingComplete?.(booking);
  };
  
  return (
    <AppointmentBookingProvider>
      <BookingFlowWidget
        initialData={initialData}
        onSuccess={handleBookingSuccess}
        {...props}
      />
    </AppointmentBookingProvider>
  );
};
```

## User Experience Design

### 1. Booking Flow UX

**Responsive Design Strategy**:
- **Mobile-First**: Touch-optimized interface
- **Progressive Enhancement**: Desktop features
- **Adaptive Layout**: Sidebar vs full-screen
- **Gesture Support**: Swipe navigation

**User Journey Optimization**:
```typescript
// Flow state with progress tracking
interface BookingProgress {
  currentStep: number;
  totalSteps: number;
  completedSteps: boolean[];
  canGoBack: boolean;
  canGoForward: boolean;
  estimatedTimeRemaining: number; // seconds
}

// UX enhancements
const useBookingUX = () => ({
  // Progress indication
  progress: calculateProgress(),
  
  // Navigation helpers
  goToStep: (step: number) => void,
  goBack: () => void,
  goForward: () => void,
  
  // Form helpers
  saveAndContinue: () => void,
  autoSave: true,
  
  // Error handling
  showError: (message: string) => void,
  clearErrors: () => void,
  
  // Loading states
  isLoading: boolean,
  loadingMessage: string
});
```

### 2. Calendar Integration UX

**Calendar View Options**:
- **Month View**: Traditional calendar grid
- **Week View**: Detailed week schedule
- **List View**: Scrollable time slots
- **Compact View**: Mobile-optimized

**Time Selection UX**:
```typescript
interface TimeSlotDisplayOptions {
  // Display format
  timeFormat: "12h" | "24h";
  timezone: string;
  
  // Grouping options
  groupByDay: boolean;
  showAvailableCount: boolean;
  highlightRecommended: boolean;
  
  // Interaction
  allowMultiSelect: boolean;
  showBufferTimes: boolean;
  enableQuickSelect: boolean;
}
```

## Performance Architecture

### 1. Data Fetching Strategy

**Smart Loading Pattern**:
```typescript
// Prefetch availability for visible dates
const useAvailabilityPrefetch = (eventTypeId: string) => {
  const [selectedDate, setSelectedDate] = useState<string>();
  
  // Prefetch current month
  useQuery({
    queryKey: ['availability', eventTypeId, currentMonth],
    queryFn: () => fetchMonthAvailability(eventTypeId, currentMonth),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
  
  // Prefetch next month when getting close to end
  useQuery({
    queryKey: ['availability', eventTypeId, nextMonth],
    queryFn: () => fetchMonthAvailability(eventTypeId, nextMonth),
    enabled: shouldPrefetchNextMonth(selectedDate),
    staleTime: 5 * 60 * 1000,
  });
};
```

**Caching Strategy**:
```typescript
// Cache availability data with intelligent invalidation
const availabilityCache = {
  // Cache key: eventTypeId:date
  keys: new Map<string, AvailabilityData>(),
  
  // Invalidation rules
  invalidateOnBooking: (booking: Booking) => {
    const date = format(booking.startTime, 'yyyy-MM-dd');
    const key = `${booking.eventTypeId}:${date}`;
    this.keys.delete(key);
  },
  
  // Background refresh
  refreshStale: () => {
    // Refresh data older than 5 minutes
  }
};
```

### 2. Component Optimization

**Memoization Strategy**:
```typescript
// Expensive calculations
const availableSlots = useMemo(() => 
  calculateAvailableSlots(availability, bookings, eventType),
  [availability, bookings, eventType]
);

// Component memoization
const TimeSlot = memo(({ slot, onSelect, isSelected }: TimeSlotProps) => {
  return (
    <button
      onClick={() => onSelect(slot.time)}
      className={timeSlotClassName(isSelected)}
    >
      {formatTime(slot.time)}
    </button>
  );
});

// Callback stability
const handleTimeSelect = useCallback(
  (time: string) => setSelectedTime(time),
  []
);
```

## Error Handling Architecture

### 1. Error Boundary Strategy

**Hierarchical Error Boundaries**:
```typescript
// Module-level error boundary
<AppointmentBookingErrorBoundary>
  <BookingFlowProvider>
    
    {/* Flow-level error boundary */}
    <BookingFlowErrorBoundary>
      <DateSelector />
      <TimeSlotSelector />
    </BookingFlowErrorBoundary>
    
    {/* Form-level error boundary */}
    <BookingFormErrorBoundary>
      <BookingForm />
    </BookingFormErrorBoundary>
    
  </BookingFlowProvider>
</AppointmentBookingErrorBoundary>
```

### 2. Error Recovery Patterns

**Graceful Degradation**:
```typescript
const BookingCalendar = ({ eventTypeId }: Props) => {
  const { data: availability, error, isLoading } = useAvailability(eventTypeId);
  
  if (error) {
    // Fallback to simple date picker
    return <SimpleDatePicker onDateSelect={handleDateSelect} />;
  }
  
  if (isLoading) {
    return <CalendarSkeleton />;
  }
  
  return <FullCalendar availability={availability} />;
};
```

## Security Architecture

### 1. Input Validation

**Multi-Layer Validation**:
```typescript
// 1. Client-side validation (Valibot)
const bookingSchema = v.object({
  attendeeName: v.pipe(v.string(), v.minLength(2), v.maxLength(100)),
  attendeeEmail: v.pipe(v.string(), v.email()),
  selectedTime: v.pipe(v.string(), v.isoDateTime()),
  eventTypeId: v.pipe(v.string(), v.uuid()),
});

// 2. Component-level validation
const BookingForm = () => {
  const form = useForm({
    schema: bookingSchema,
    onSubmit: async (data) => {
      // 3. Server-side validation before API call
      const validatedData = await validateBookingData(data);
      await createBooking(validatedData);
    }
  });
};
```

### 2. Data Protection

**Sensitive Data Handling**:
```typescript
// Sanitize user inputs
const sanitizeBookingData = (data: BookingFormData): SanitizedBookingData => ({
  attendeeName: sanitizeString(data.attendeeName),
  attendeeEmail: normalizeEmail(data.attendeeEmail),
  notes: sanitizeHtml(data.notes),
  // Remove any potentially harmful data
});

// Encrypt sensitive data in store
const bookingStore = create<BookingStore>((set) => ({
  setAttendeeData: (data: AttendeeData) => {
    const encryptedData = encryptSensitiveFields(data);
    set({ attendeeData: encryptedData });
  }
}));
```

## Testing Architecture

### 1. Component Testing Strategy

**Testing Pyramid for Booking Module**:
```typescript
// Unit tests - Individual components
describe('TimeSlotSelector', () => {
  it('should display available time slots', () => {
    render(<TimeSlotSelector slots={mockSlots} />);
    expect(screen.getByText('9:00 AM')).toBeInTheDocument();
  });
  
  it('should handle slot selection', () => {
    const onSelect = jest.fn();
    render(<TimeSlotSelector slots={mockSlots} onSelect={onSelect} />);
    
    fireEvent.click(screen.getByText('9:00 AM'));
    expect(onSelect).toHaveBeenCalledWith('09:00');
  });
});

// Integration tests - Component interactions
describe('BookingFlow', () => {
  it('should complete booking flow', async () => {
    render(<BookingFlowProvider><BookingFlow /></BookingFlowProvider>);
    
    // Select date
    fireEvent.click(screen.getByText('15'));
    
    // Select time
    await waitFor(() => screen.getByText('9:00 AM'));
    fireEvent.click(screen.getByText('9:00 AM'));
    
    // Fill form
    fireEvent.change(screen.getByLabelText('Name'), { 
      target: { value: 'John Doe' } 
    });
    
    // Submit
    fireEvent.click(screen.getByText('Book Appointment'));
    
    await waitFor(() => 
      expect(screen.getByText('Booking Confirmed')).toBeInTheDocument()
    );
  });
});
```

### 2. Store Testing

**Zustand Store Testing**:
```typescript
describe('BookingStore', () => {
  beforeEach(() => {
    useBookingStore.setState(initialState);
  });
  
  it('should update selected time', () => {
    const { selectTime } = useBookingStore.getState();
    selectTime('09:00');
    
    expect(useBookingStore.getState().selectedTime).toBe('09:00');
  });
  
  it('should progress through booking flow', () => {
    const store = useBookingStore.getState();
    
    store.selectEventType(mockEventType);
    expect(store.currentStep).toBe('selecting_date');
    
    store.selectDate('2024-03-15');
    expect(store.currentStep).toBe('selecting_time');
    
    store.selectTime('09:00');
    expect(store.currentStep).toBe('filling_details');
  });
});
```

## Deployment Architecture

### 1. Build Optimization

**Bundle Splitting Strategy**:
```typescript
// next.config.js
module.exports = {
  experimental: {
    optimizePackageImports: [
      '@calcom/ui',
      'date-fns',
      'react-query'
    ]
  },
  
  webpack: (config) => {
    // Split booking module into separate chunk
    config.optimization.splitChunks.cacheGroups.bookingModule = {
      test: /[\\/]appointments[\\/]/,
      name: 'booking-module',
      chunks: 'all',
      priority: 10
    };
    
    return config;
  }
};
```

### 2. Environment Configuration

**Environment-Specific Settings**:
```typescript
// Configuration for different environments
const appointmentConfig = {
  development: {
    enableDebugMode: true,
    mockDataEnabled: true,
    cacheTimeout: 1000, // 1 second for fast development
  },
  
  production: {
    enableDebugMode: false,
    mockDataEnabled: false,
    cacheTimeout: 300000, // 5 minutes
    enableAnalytics: true,
  }
};
```

## Migration and Implementation Strategy

### 1. Phase 1: Foundation (Week 1-2)
- Set up basic module structure
- Implement core Zustand stores
- Create basic UI components
- Add TypeScript definitions

### 2. Phase 2: Core Functionality (Week 3-4)
- Implement booking flow components
- Add date/time selection logic
- Create booking form system
- Integrate with mock data

### 3. Phase 3: CRM Integration (Week 5-6)
- Connect with existing CRM stores
- Implement contact integration
- Add task automation
- Create reporting views

### 4. Phase 4: Polish & Optimization (Week 7-8)
- Performance optimization
- Accessibility improvements
- Mobile optimization
- Comprehensive testing

## Next Steps

1. **Review Architecture**: Validate approach with team
2. **Create Component Specifications**: Detailed component API design
3. **Set Up Development Environment**: Configure build tools and dependencies
4. **Begin Implementation**: Start with Phase 1 foundation work

## Related Documents

- [02-component-architecture.md](./02-component-architecture.md) - Detailed component design
- [03-state-management-design.md](./03-state-management-design.md) - Store architecture
- [04-ui-component-design.md](./04-ui-component-design.md) - UI component specifications
- [../implementation/01-development-roadmap.md](../implementation/01-development-roadmap.md) - Implementation timeline
