# Event Type Management - Appointment Booking Module

## Table of Contents
1. [Event Type Overview](#event-type-overview)
2. [Data Structures](#data-structures)
3. [Creation Workflow](#creation-workflow)
4. [Configuration Options](#configuration-options)
5. [Management Operations](#management-operations)
6. [Validation Rules](#validation-rules)

## Event Type Overview

Event Types are the foundation of the appointment booking system. They define the structure, duration, pricing, and availability rules for different types of appointments that can be booked.

### Core Concepts
- **Event Type**: A template for bookable appointments
- **Slug**: URL-friendly identifier for public booking pages
- **Duration**: Fixed or variable appointment lengths
- **Locations**: Where appointments take place (in-person, video, phone)
- **Booking Fields**: Custom information collected from bookers
- **Availability**: When appointments can be scheduled

## Data Structures

### Core Event Type Interface
```typescript
interface EventType {
  id: string;
  title: string;
  slug: string;
  description?: string;
  
  // Timing
  length: number; // Duration in minutes
  offsetStart: number; // Buffer before appointment
  offsetEnd: number; // Buffer after appointment
  
  // Availability
  scheduleId?: string;
  availability?: AvailabilitySettings;
  
  // Booking Rules
  minimumBookingNotice: number; // Minutes before booking allowed
  maximumBookingNotice?: number; // Maximum days in advance
  slotInterval?: number; // Time between available slots
  
  // Limits
  bookingLimits?: BookingLimits;
  durationLimits?: DurationLimits;
  
  // Pricing
  price?: number;
  currency?: string;
  
  // Locations
  locations: EventLocation[];
  
  // Form Configuration
  bookingFields: BookingField[];
  
  // Settings
  hidden: boolean;
  disabled: boolean;
  requiresConfirmation: boolean;
  disableGuests: boolean;
  
  // Metadata
  color?: string;
  metadata: EventTypeMetadata;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
}
```

### Booking Limits
```typescript
interface BookingLimits {
  // Per time period limits
  PER_DAY?: number;
  PER_WEEK?: number;
  PER_MONTH?: number;
  PER_YEAR?: number;
}

interface DurationLimits {
  // Minimum/maximum booking duration
  PER_DAY?: { min?: number; max?: number };
  PER_WEEK?: { min?: number; max?: number };
  PER_MONTH?: { min?: number; max?: number };
  PER_YEAR?: { min?: number; max?: number };
}
```

### Location Types
```typescript
interface EventLocation {
  type: LocationType;
  address?: string;
  link?: string;
  phone?: string;
  displayLocationPublicly: boolean;
  credentialId?: string;
}

enum LocationType {
  IN_PERSON = 'inPerson',
  PHONE = 'phone',
  GOOGLE_MEET = 'integrations:google:meet',
  ZOOM = 'integrations:zoom',
  MS_TEAMS = 'integrations:office365_video',
  CUSTOM_VIDEO = 'link',
  CUSTOM_LOCATION = 'custom'
}
```

### Booking Fields
```typescript
interface BookingField {
  name: string;
  type: BookingFieldType;
  label: string;
  placeholder?: string;
  required: boolean;
  options?: string[]; // For select/radio fields
  validation?: FieldValidation;
  hidden?: boolean;
  defaultValue?: any;
}

enum BookingFieldType {
  TEXT = 'text',
  EMAIL = 'email',
  PHONE = 'phone',
  TEXTAREA = 'textarea',
  SELECT = 'select',
  MULTISELECT = 'multiselect',
  RADIO = 'radio',
  CHECKBOX = 'checkbox',
  NUMBER = 'number',
  DATE = 'date',
  BOOLEAN = 'boolean'
}

interface FieldValidation {
  minLength?: number;
  maxLength?: number;
  pattern?: string;
  min?: number;
  max?: number;
}
```

### Event Type Metadata
```typescript
interface EventTypeMetadata {
  // SEO
  title?: string;
  description?: string;
  
  // Appearance
  theme?: {
    brandColor?: string;
    darkBrandColor?: string;
  };
  
  // Notifications
  emailReminders?: EmailReminder[];
  smsReminders?: SmsReminder[];
  
  // Advanced Settings
  successRedirectUrl?: string;
  cancelRedirectUrl?: string;
  
  // Integration Settings
  googleAnalyticsId?: string;
  pixelId?: string;
  
  // Custom Properties
  [key: string]: any;
}
```

## Creation Workflow

### 1. Basic Information Step
```typescript
interface BasicEventTypeData {
  title: string;
  slug: string; // Auto-generated from title, editable
  description?: string;
  length: number; // Default: 30 minutes
  color?: string; // Default: primary brand color
}
```

**Validation Rules**:
- Title: 1-100 characters, required
- Slug: URL-safe, unique, 3-50 characters
- Length: 5-480 minutes (8 hours max)
- Description: 0-500 characters

### 2. Location Configuration Step
```typescript
interface LocationStep {
  locations: EventLocation[];
  defaultLocation?: string;
}
```

**Available Options**:
- In-person meetings with address
- Phone calls with number display options
- Video conferencing (Google Meet, Zoom, Teams)
- Custom video links
- Custom locations

### 3. Availability Settings Step
```typescript
interface AvailabilityStep {
  scheduleId?: string; // Use existing schedule
  customAvailability?: Schedule; // Create custom schedule
  minimumBookingNotice: number; // Default: 120 minutes
  maximumBookingNotice?: number; // Default: 60 days
  slotInterval?: number; // Default: same as duration
}
```

### 4. Booking Form Configuration Step
```typescript
interface BookingFormStep {
  bookingFields: BookingField[];
  requiresConfirmation: boolean;
  disableGuests: boolean;
  guestLimit?: number;
}
```

**Default Fields**:
- Name (required)
- Email (required)
- Additional notes (optional)

### 5. Advanced Settings Step
```typescript
interface AdvancedSettingsStep {
  // Pricing
  price?: number;
  currency?: string;
  
  // Limits
  bookingLimits?: BookingLimits;
  durationLimits?: DurationLimits;
  
  // Buffers
  offsetStart: number; // Default: 0
  offsetEnd: number; // Default: 0
  
  // Redirects
  successRedirectUrl?: string;
  cancelRedirectUrl?: string;
  
  // SEO
  metadata: EventTypeMetadata;
}
```

## Configuration Options

### Duration Settings
```typescript
interface DurationConfig {
  // Fixed duration
  length: number;
  
  // OR Multiple duration options
  multipleDurationEnabled: boolean;
  durationOptions?: number[]; // e.g., [15, 30, 45, 60]
}
```

### Availability Integration
```typescript
interface AvailabilityConfig {
  // Use global schedule
  useGlobalSchedule: boolean;
  scheduleId?: string;
  
  // OR Custom availability
  customSchedule?: {
    timezone: string;
    availability: DayAvailability[];
    dateOverrides: DateOverride[];
  };
  
  // Booking windows
  minimumBookingNotice: number; // Minutes
  maximumBookingNotice?: number; // Days
  
  // Slot configuration
  slotInterval?: number; // Minutes between slots
  minimumGapBetweenBookings?: number; // Minutes
}
```

### Pricing Configuration
```typescript
interface PricingConfig {
  enabled: boolean;
  price: number;
  currency: string;
  
  // Payment settings
  paymentRequired: boolean;
  paymentMethods: PaymentMethod[];
  
  // Stripe integration
  stripeAccountId?: string;
  stripePriceId?: string;
}
```

## Management Operations

### CRUD Operations
```typescript
interface EventTypeService {
  // Create
  create(data: CreateEventTypeData): Promise<EventType>;
  
  // Read
  getById(id: string): Promise<EventType>;
  getBySlug(slug: string): Promise<EventType>;
  getAll(filters?: EventTypeFilters): Promise<EventType[]>;
  
  // Update
  update(id: string, data: Partial<EventType>): Promise<EventType>;
  
  // Delete
  delete(id: string): Promise<void>;
  
  // Bulk operations
  bulkUpdate(ids: string[], data: Partial<EventType>): Promise<EventType[]>;
  bulkDelete(ids: string[]): Promise<void>;
}
```

### Status Management
```typescript
interface EventTypeStatus {
  // Visibility
  toggleVisibility(id: string, hidden: boolean): Promise<void>;
  
  // Availability
  toggleAvailability(id: string, disabled: boolean): Promise<void>;
  
  // Bulk status changes
  bulkToggleVisibility(ids: string[], hidden: boolean): Promise<void>;
}
```

### Duplication
```typescript
interface DuplicationOptions {
  includeAvailability: boolean;
  includeBookingFields: boolean;
  includeLocations: boolean;
  includePricing: boolean;
  newTitle?: string;
  newSlug?: string;
}

function duplicateEventType(
  sourceId: string, 
  options: DuplicationOptions
): Promise<EventType>;
```

## Validation Rules

### Title Validation
- Required field
- 1-100 characters
- No special characters except: - _ ( ) & ,
- Must be unique per user

### Slug Validation
- Required field
- 3-50 characters
- Lowercase letters, numbers, hyphens only
- Must start and end with alphanumeric character
- Must be globally unique
- Auto-generated from title, manually editable

### Duration Validation
- Minimum: 5 minutes
- Maximum: 480 minutes (8 hours)
- Must be divisible by 5
- Multiple durations must be in ascending order

### Location Validation
- At least one location required
- Video links must be valid URLs
- Phone numbers must be valid format
- Addresses must be non-empty for in-person

### Booking Field Validation
- Name and email fields are always required
- Custom field names must be unique
- Select/radio options must have at least 2 choices
- Validation patterns must be valid regex

### Availability Validation
- Minimum booking notice: 0-10080 minutes (1 week)
- Maximum booking notice: 1-365 days
- Slot interval must be ≤ event duration
- Buffer times must be 0-120 minutes

### Pricing Validation
- Price must be ≥ 0
- Currency must be valid ISO code
- Stripe integration required for paid events

## Error Handling

### Validation Errors
```typescript
interface ValidationError {
  field: string;
  message: string;
  code: string;
}

interface EventTypeValidationResult {
  valid: boolean;
  errors: ValidationError[];
}
```

### Common Error Scenarios
- Duplicate slug conflicts
- Invalid availability schedules
- Missing required integrations
- Pricing configuration errors
- Location setup issues

This comprehensive event type management system provides the flexibility and control needed for diverse appointment booking scenarios while maintaining data integrity and user experience standards.
