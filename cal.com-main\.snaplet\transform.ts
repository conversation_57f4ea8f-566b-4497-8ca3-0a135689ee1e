// eslint-disable-next-line @typescript-eslint/triple-slash-reference
/// <reference path=".snaplet/snaplet.d.ts" />
// This config was generated by Snaplet make sure to check it over before using it.
import { copycat as c } from "@snaplet/copycat";
import { defineConfig } from "snaplet";

// c.setHashKey(REPLACE_ME_WITH_YOUR_HASH_KEY);

function hasStringProp<T extends string>(x: unknown, key: T): x is { [key in T]: string } {
  return !!x && typeof x === "object" && key in x;
}

function replaceKeyIfExists<T extends string>(x: object, key: T) {
  if (hasStringProp(x, key)) {
    return { ...x, [key]: c.uuid(x[key]) };
  }
  return x;
}

function generateSlug(x: string) {
  return c.words(x, { max: 3 }).split(" ").join("-");
}

function replaceSensitiveKeys(record: object) {
  return {
    ...record,
    ...replaceKeyIfExists(record, "client_id"),
    ...replaceKeyIfExists(record, "client_secret"),
    ...replaceKeyIfExists(record, "public_key"),
    ...replaceKeyIfExists(record, "api_key"),
    ...replaceKeyIfExists(record, "signing_secret"),
    ...replaceKeyIfExists(record, "access_token"),
    ...replaceKeyIfExists(record, "refresh_token"),
    ...replaceKeyIfExists(record, "stripe_user_id"),
    ...replaceKeyIfExists(record, "stripe_publishable_key"),
    ...replaceKeyIfExists(record, "accessToken"),
    ...replaceKeyIfExists(record, "refreshToken"),
    ...replaceKeyIfExists(record, "bot_user_id"),
    ...replaceKeyIfExists(record, "app_id"),
  };
}

const generateUsername = (x: string) => `${c.firstName(x)}-${c.lastName(x)}${c.int(x, { min: 2, max: 99 })}`;

export default defineConfig({
  transform: {
    $mode: "unsafe",
    public: {
      Account({ row }) {
        return {
          refresh_token: c.uuid(row.refresh_token),
          access_token: c.uuid(row.access_token),
          expires_at: c.int(row.expires_at, {
            min: 0,
            max: Math.pow(4, 8) - 1,
          }),
          token_type: c.uuid(row.token_type),
          id_token: c.uuid(row.id_token),
        };
      },
      ApiKey: ({ row }) => ({
        hashedKey: c.uuid(row.hashedKey),
        note: c.fullName(row.note),
        createdAt: c.dateString(row.createdAt, {
          minYear: 2020,
        }),
      }),
      App: ({ row }) => ({
        keys: replaceSensitiveKeys(row.keys),
      }),
      App_RoutingForms_Form({ row }) {
        return {
          name: c.fullName(row.name),
        };
      },
      Attendee: ({ row }) => ({
        email: c.email(row.email),
        name: c.fullName(row.name),
        timeZone: c.timezone(row.timeZone),
        locale: c.fullName(row.locale),
      }),
      Availability({ row }) {
        return {
          startTime: c
            .dateString(row.startTime, {
              minYear: 2020,
            })
            .slice(11, 19),
          endTime: c
            .dateString(row.endTime, {
              minYear: 2020,
            })
            .slice(11, 19),
        };
      },
      Booking({ row }) {
        return {
          title: c.fullName(row.title),
          startTime: c.dateString(row.startTime, {
            minYear: 2020,
          }),
          endTime: c.dateString(row.endTime, {
            minYear: 2020,
          }),
          location: c.sentence(row.location),
          metadata: {
            [c.word(row.metadata)]: c.words(row.metadata),
          },
        };
      },
      Credential: ({ row }) => ({
        key: typeof row.key === "string" ? c.uuid(row.key) : replaceSensitiveKeys(row.key),
      }),
      EventType: ({ row }) => ({
        slug: generateSlug(row.slug),
        timeZone: c.timezone(row.timeZone),
        eventName: c.words(row.eventName, { max: 3 }),
        currency: c.sentence(row.currency),
      }),
      EventTypeCustomInput({ row }) {
        return {
          label: c.fullName(row.label),
        };
      },
      Feature({ row }) {
        return {
          slug: c.uuid(row.slug),
        };
      },
      InstantMeetingToken({ row }) {
        return {
          token: c.uuid(row.token),
        };
      },
      OAuthClient({ row }) {
        return {
          name: c.fullName(row.name),
        };
      },
      OutOfOfficeEntry({ row }) {
        return {
          start: c.dateString(row.start, {
            minYear: 2020,
          }),
        };
      },
      Payment({ row }) {
        return {
          amount: c.int(row.amount, {
            min: 0,
            max: Math.pow(4, 8) - 1,
          }),
          currency: c.sentence(row.currency),
          data: {
            [c.word(row.data)]: c.words(row.data),
          },
        };
      },
      ResetPasswordRequest: ({ row }) => ({
        email: c.email(row.email),
      }),
      Schedule: ({ row }) => ({
        name: c.fullName(row.name),
        timeZone: c.timezone(row.timeZone),
      }),
      Session({ row }) {
        return {
          sessionToken: c.uuid(row.sessionToken),
        };
      },
      Team: ({ row }) => ({
        bio: c.sentence(row.bio),
        name: c.words(row.name, { max: 2 }),
        slug: generateSlug(row.slug),
        timeZone: c.timezone(row.timeZone),
        logoUrl: c.username(row.logoUrl),
      }),
      TempOrgRedirect({ row }) {
        return {
          from: c.dateString(row.from, {
            maxYear: 1999,
          }),
          toUrl: c.city(row.toUrl),
        };
      },
      VerificationToken({ row }) {
        return {
          id: c
            .int(row.id, {
              min: 1,
              max: Math.pow(4, 8) - 1,
            })
            .toString(),
          identifier: c.uuid(row.identifier),
          token: c.uuid(row.token),
          expires: c.dateString(row.expires, {
            minYear: 2020,
          }),
        };
      },
      VerifiedNumber({ row }) {
        return {
          phoneNumber: c.phoneNumber(row.phoneNumber),
        };
      },
      Webhook({ row }) {
        return {
          subscriberUrl: c.url(row.subscriberUrl),
          secret: c.streetAddress(row.secret),
        };
      },
      WebhookScheduledTriggers({ row }) {
        return {
          jobName: c.fullName(row.jobName),
          payload: c.password(row.payload),
        };
      },
      WorkflowStep({ row }) {
        return {
          sendTo: c.oneOf(row.sendTo, [
            "Man",
            "Woman",
            "Transgender",
            "Non-binary/non-conforming",
            "Not specified",
          ]),
          sender: c.oneOf(row.sender, [
            "Man",
            "Woman",
            "Transgender",
            "Non-binary/non-conforming",
            "Not specified",
          ]),
        };
      },
      users: ({ row }) =>
        row.role !== "ADMIN"
          ? {
              bio: c.sentence(row.bio),
              email: c.email(row.email),
              name: c.fullName(row.name),
              password: c.password(row.password),
              timeZone: c.timezone(row.timeZone),
              username: generateUsername(row.username),
              metadata: {
                [c.word(row.metadata)]: c.words(row.metadata),
              },
            }
          : row,
    },
  },
  subset: {
    enabled: true,
    version: "3",
    targets: [
      {
        table: "public.users",
        rowLimit: 100,
      },
    ],
    keepDisconnectedTables: false,
    followNullableRelations: true,
    maxCyclesLoop: 0,
    eager: false,
    taskSortAlgorithm: "children",
  },
});
