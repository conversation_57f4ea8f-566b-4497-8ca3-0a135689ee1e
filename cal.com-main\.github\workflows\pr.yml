name: PR Update

on:
  pull_request_target:
    types: [opened, synchronize, reopened, labeled]
    branches:
      - main
      - gh-actions-test-branch
  workflow_dispatch:

permissions:
  actions: write
  contents: read

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  changes:
    name: Detect changes
    runs-on: buildjet-2vcpu-ubuntu-2204
    permissions:
      pull-requests: read
    outputs:
      has-files-requiring-all-checks: ${{ steps.filter.outputs.has-files-requiring-all-checks }}
      commit-sha: ${{ steps.get_sha.outputs.commit-sha }}
    steps:
      - uses: actions/checkout@v4
      - uses: ./.github/actions/dangerous-git-checkout
      - uses: dorny/paths-filter@v3
        id: filter
        with:
          filters: |
            has-files-requiring-all-checks:
              - "!(**.md|.github/CODEOWNERS|docs/**|help/**|apps/web/public/static/locales/**/common.json)"
      - name: Get Latest Commit SHA
        id: get_sha
        run: |
          echo "commit-sha=$(git rev-parse HEAD)" >> $GITHUB_OUTPUT

  check-label:
    needs: [changes]
    runs-on: buildjet-2vcpu-ubuntu-2204
    name: Check for E2E label
    permissions:
      pull-requests: read
    outputs:
      run-e2e: ${{ steps.check-if-pr-has-label.outputs.run-e2e == 'true' && (github.event.action != 'labeled' || github.event.label.name == 'ready-for-e2e') }}
    steps:
      - name: Check if PR exists with ready-for-e2e label for this SHA
        id: check-if-pr-has-label
        uses: actions/github-script@v7
        with:
          script: |
            let labels = [];

            if (context.payload.pull_request) {
              labels = context.payload.pull_request.labels;
            } else {
              try {
                const sha = '${{ needs.changes.outputs.commit-sha }}';
                console.log('sha', sha);
                const { data: prs } = await github.rest.repos.listPullRequestsAssociatedWithCommit({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  commit_sha: sha
                });

                if (prs.length === 0) {
                  core.setOutput('run-e2e', false);
                  console.log(`No pull requests found for commit SHA ${sha}`);
                  return;
                }

                const pr = prs[0];
                console.log(`PR number: ${pr.number}`);
                console.log(`PR title: ${pr.title}`);
                console.log(`PR state: ${pr.state}`);
                console.log(`PR URL: ${pr.html_url}`);

                labels = pr.labels;
              }
              catch (e) {
                core.setOutput('run-e2e', false);
                console.log(e);
              }
            }

            const labelFound = labels.map(l => l.name).includes('ready-for-e2e');
            console.log('Found the label?', labelFound);
            core.setOutput('run-e2e', labelFound);

  deps:
    name: Install dependencies
    needs: [changes, check-label]
    if: ${{ needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    uses: ./.github/workflows/yarn-install.yml

  type-check:
    name: Type check
    needs: [changes, check-label, deps]
    if: ${{ needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    uses: ./.github/workflows/check-types.yml
    secrets: inherit

  lint:
    name: Linters
    needs: [changes, check-label, deps]
    if: ${{ needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    uses: ./.github/workflows/lint.yml
    secrets: inherit

  unit-test:
    name: Tests
    needs: [changes, check-label, deps]
    if: ${{ needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    uses: ./.github/workflows/unit-tests.yml
    secrets: inherit

  build-api-v1:
    name: Production builds
    needs: [changes, check-label, deps]
    if: ${{ needs.check-label.outputs.run-e2e == 'true' && needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    uses: ./.github/workflows/api-v1-production-build.yml
    secrets: inherit

  build-api-v2:
    name: Production builds
    needs: [changes, check-label, deps]
    if: ${{ needs.check-label.outputs.run-e2e == 'true' && needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    uses: ./.github/workflows/api-v2-production-build.yml
    secrets: inherit

  build-atoms:
    name: Production builds
    needs: [changes, check-label, deps]
    if: ${{ needs.check-label.outputs.run-e2e == 'true' && needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    uses: ./.github/workflows/atoms-production-build.yml
    secrets: inherit

  build-docs:
    name: Production builds
    needs: [changes, check-label, deps]
    if: ${{ needs.check-label.outputs.run-e2e == 'true' && needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    uses: ./.github/workflows/docs-build.yml
    secrets: inherit

  build:
    name: Production builds
    needs: [changes, check-label, deps]
    if: ${{ needs.check-label.outputs.run-e2e == 'true' && needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    uses: ./.github/workflows/production-build-without-database.yml
    secrets: inherit

  integration-test:
    name: Tests
    needs: [changes, check-label, build, build-api-v1, build-api-v2]
    if: ${{ needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    uses: ./.github/workflows/integration-tests.yml
    secrets: inherit

  e2e:
    name: Tests
    needs: [changes, check-label, build, build-api-v1, build-api-v2]
    if: ${{ needs.check-label.outputs.run-e2e == 'true' && needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    uses: ./.github/workflows/e2e.yml
    secrets: inherit

  e2e-api-v2:
    name: Tests
    needs: [changes, check-label, build, build-api-v1, build-api-v2]
    if: ${{ needs.check-label.outputs.run-e2e == 'true' && needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    uses: ./.github/workflows/e2e-api-v2.yml
    secrets: inherit

  e2e-app-store:
    name: Tests
    needs: [changes, check-label, build, build-api-v1, build-api-v2]
    if: ${{ needs.check-label.outputs.run-e2e == 'true' && needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    uses: ./.github/workflows/e2e-app-store.yml
    secrets: inherit

  e2e-embed:
    name: Tests
    needs: [changes, check-label, build, build-api-v1, build-api-v2]
    if: ${{ needs.check-label.outputs.run-e2e == 'true' && needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    uses: ./.github/workflows/e2e-embed.yml
    secrets: inherit

  e2e-embed-react:
    name: Tests
    needs: [changes, check-label, build, build-api-v1, build-api-v2]
    if: ${{ needs.check-label.outputs.run-e2e == 'true' && needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    uses: ./.github/workflows/e2e-embed-react.yml
    secrets: inherit

  analyze:
    name: Analyze Build
    needs: [build]
    uses: ./.github/workflows/nextjs-bundle-analysis.yml
    secrets: inherit

  merge-reports:
    name: Merge reports
    if: ${{ !cancelled() && needs.check-label.outputs.run-e2e == 'true' && needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    needs: [changes, check-label, e2e, e2e-embed, e2e-embed-react, e2e-app-store]
    uses: ./.github/workflows/merge-reports.yml
    secrets: inherit

  publish-report:
    name: Publish HTML report
    if: ${{ !cancelled() && needs.check-label.outputs.run-e2e == 'true' && needs.changes.outputs.has-files-requiring-all-checks == 'true' }}
    permissions:
      contents: write
      issues: write
      pull-requests: write
    needs: [changes, check-label, merge-reports]
    uses: ./.github/workflows/publish-report.yml
    secrets: inherit

  cleanup-report:
    name: Cleanup HTML report
    if: ${{ !cancelled() && needs.check-label.outputs.run-e2e == 'true' && (github.event.pull_request.merged == true || github.event.pull_request.state == 'closed') }}
    permissions:
      contents: write
      issues: write
      pull-requests: write
    needs: [changes, check-label, publish-report]
    uses: ./.github/workflows/cleanup-report.yml
    secrets: inherit

  required:
    needs:
      [
        changes,
        lint,
        type-check,
        unit-test,
        integration-test,
        check-label,
        build,
        build-api-v1,
        build-api-v2,
        build-atoms,
        build-docs,
        e2e,
        e2e-api-v2,
        e2e-embed,
        e2e-embed-react,
        e2e-app-store,
      ]
    if: always()
    runs-on: buildjet-2vcpu-ubuntu-2204
    steps:
      - name: fail if conditional jobs failed
        if: needs.changes.outputs.has-files-requiring-all-checks == 'true' && (contains(needs.*.result, 'failure') || contains(needs.*.result, 'skipped') || contains(needs.*.result, 'cancelled'))
        run: exit 1
