name: Integration
on:
  workflow_call:
permissions:
  contents: read
env:
  NODE_OPTIONS: --max-old-space-size=4096
  ALLOWED_HOSTNAMES: ${{ vars.CI_ALLOWED_HOSTNAMES }}
  CALENDSO_ENCRYPTION_KEY: ${{ secrets.CI_CALENDSO_ENCRYPTION_KEY }}
  DAILY_API_KEY: ${{ secrets.CI_DAILY_API_KEY }}
  DATABASE_URL: ${{ secrets.CI_DATABASE_URL }}
  DATABASE_DIRECT_URL: ${{ secrets.CI_DATABASE_URL }}
  DEPLOYSENTINEL_API_KEY: ${{ secrets.DEPLOYSENTINEL_API_KEY }}
  E2E_TEST_APPLE_CALENDAR_EMAIL: ${{ secrets.E2E_TEST_APPLE_CALENDAR_EMAIL }}
  E2E_TEST_APPLE_CALENDAR_PASSWORD: ${{ secrets.E2E_TEST_APPLE_CALENDAR_PASSWORD }}
  E2E_TEST_MAILHOG_ENABLED: ${{ vars.E2E_TEST_MAILHOG_ENABLED }}
  GOOGLE_API_CREDENTIALS: ${{ secrets.CI_GOOGLE_API_CREDENTIALS }}
  EMAIL_SERVER_HOST: ${{ secrets.CI_EMAIL_SERVER_HOST }}
  EMAIL_SERVER_PORT: ${{ secrets.CI_EMAIL_SERVER_PORT }}
  EMAIL_SERVER_USER: ${{ secrets.CI_EMAIL_SERVER_USER }}
  EMAIL_SERVER_PASSWORD: ${{ secrets.CI_EMAIL_SERVER_PASSWORD}}
  GOOGLE_LOGIN_ENABLED: ${{ vars.CI_GOOGLE_LOGIN_ENABLED }}
  NEXTAUTH_SECRET: ${{ secrets.CI_NEXTAUTH_SECRET }}
  NEXTAUTH_URL: ${{ secrets.CI_NEXTAUTH_URL }}
  NEXT_PUBLIC_API_V2_URL: ${{ secrets.CI_NEXT_PUBLIC_API_V2_URL }}
  NEXT_PUBLIC_API_V2_ROOT_URL: ${{ secrets.CI_NEXT_PUBLIC_API_V2_ROOT_URL }}
  NEXT_PUBLIC_IS_E2E: ${{ vars.CI_NEXT_PUBLIC_IS_E2E }}
  NEXT_PUBLIC_ORG_SELF_SERVE_ENABLED: ${{ vars.CI_NEXT_PUBLIC_ORG_SELF_SERVE_ENABLED }}
  NEXT_PUBLIC_STRIPE_PUBLIC_KEY: ${{ secrets.CI_NEXT_PUBLIC_STRIPE_PUBLIC_KEY }}
  NEXT_PUBLIC_WEBAPP_URL: ${{ vars.CI_NEXT_PUBLIC_WEBAPP_URL }}
  NEXT_PUBLIC_WEBSITE_URL: ${{ vars.CI_NEXT_PUBLIC_WEBSITE_URL }}
  PAYMENT_FEE_FIXED: ${{ vars.CI_PAYMENT_FEE_FIXED }}
  PAYMENT_FEE_PERCENTAGE: ${{ vars.CI_PAYMENT_FEE_PERCENTAGE }}
  SAML_ADMINS: ${{ secrets.CI_SAML_ADMINS }}
  SAML_DATABASE_URL: ${{ secrets.CI_SAML_DATABASE_URL }}
  STRIPE_PRIVATE_KEY: ${{ secrets.CI_STRIPE_PRIVATE_KEY }}
  STRIPE_CLIENT_ID: ${{ secrets.CI_STRIPE_CLIENT_ID }}
  STRIPE_WEBHOOK_SECRET: ${{ secrets.CI_STRIPE_WEBHOOK_SECRET }}
  SENDGRID_API_KEY: ${{ secrets.CI_SENDGRID_API_KEY }}
  SENDGRID_EMAIL: ${{ secrets.CI_SENDGRID_EMAIL }}
  TURBO_TOKEN: ${{ secrets.TURBO_TOKEN }}
  TURBO_TEAM: ${{ secrets.TURBO_TEAM }}
jobs:
  integration:
    timeout-minutes: 20
    name: Integration
    runs-on: buildjet-8vcpu-ubuntu-2204
    services:
      postgres:
        image: postgres:13
        credentials:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: calendso
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      mailhog:
        image: mailhog/mailhog:v1.0.1
        credentials:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
        ports:
          - 8025:8025
          - 1025:1025
    strategy:
      fail-fast: false
    steps:
      - uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - uses: actions/checkout@v4
      - uses: ./.github/actions/dangerous-git-checkout
      - uses: ./.github/actions/yarn-install
      - uses: ./.github/actions/cache-db
      - name: Run Tests
        run: yarn test -- --integrationTestsOnly
      # TODO: Generate test results so we can upload them
      # - name: Upload Test Results
      #   if: ${{ always() }}
      #   uses: actions/upload-artifact@v4
      #   with:
      #     name: test-results
      #     path: test-results
