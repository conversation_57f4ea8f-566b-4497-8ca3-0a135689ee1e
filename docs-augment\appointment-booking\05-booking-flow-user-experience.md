# Booking Flow & User Experience - Appointment Booking Module

## Table of Contents
1. [User Journey Overview](#user-journey-overview)
2. [Booking Flow States](#booking-flow-states)
3. [Public Booking Interface](#public-booking-interface)
4. [Form Handling & Validation](#form-handling--validation)
5. [Confirmation & Follow-up](#confirmation--follow-up)
6. [Error Handling & Recovery](#error-handling--recovery)

## User Journey Overview

The booking flow encompasses the complete visitor experience from discovering an event type to receiving booking confirmation. The system supports multiple entry points and provides a seamless, accessible experience across all devices.

### Entry Points
1. **Direct Event Type URL**: `/[username]/[event-slug]`
2. **Event Type Selection**: `/[username]` → choose event type
3. **Embedded Booking Widget**: Third-party website integration
4. **QR Code**: Mobile-optimized booking experience

### Core User Journey
```
Discovery → Event Selection → Date/Time Selection → Form Completion → Confirmation
```

## Booking Flow States

### State Management
```typescript
enum BookingState {
  LOADING = 'loading',
  EVENT_SELECTION = 'event_selection',
  DATE_SELECTION = 'date_selection', 
  TIME_SELECTION = 'time_selection',
  FORM_COMPLETION = 'form_completion',
  CONFIRMATION = 'confirmation',
  SUCCESS = 'success',
  ERROR = 'error'
}

interface BookingFlowState {
  currentState: BookingState;
  selectedEventType?: EventType;
  selectedDate?: string;
  selectedTime?: string;
  formData?: BookingFormData;
  errors?: BookingError[];
  isLoading: boolean;
  canGoBack: boolean;
  canGoForward: boolean;
}
```

### State Transitions
```typescript
interface StateTransition {
  from: BookingState;
  to: BookingState;
  trigger: string;
  validation?: () => boolean;
  sideEffects?: () => void;
}

const BOOKING_FLOW_TRANSITIONS: StateTransition[] = [
  {
    from: BookingState.LOADING,
    to: BookingState.EVENT_SELECTION,
    trigger: 'EVENT_TYPES_LOADED'
  },
  {
    from: BookingState.EVENT_SELECTION,
    to: BookingState.DATE_SELECTION,
    trigger: 'EVENT_TYPE_SELECTED',
    validation: () => !!selectedEventType
  },
  {
    from: BookingState.DATE_SELECTION,
    to: BookingState.TIME_SELECTION,
    trigger: 'DATE_SELECTED',
    validation: () => !!selectedDate && isDateAvailable(selectedDate)
  },
  {
    from: BookingState.TIME_SELECTION,
    to: BookingState.FORM_COMPLETION,
    trigger: 'TIME_SELECTED',
    validation: () => !!selectedTime && isTimeSlotAvailable(selectedTime)
  },
  {
    from: BookingState.FORM_COMPLETION,
    to: BookingState.CONFIRMATION,
    trigger: 'FORM_SUBMITTED',
    validation: () => validateBookingForm(formData)
  },
  {
    from: BookingState.CONFIRMATION,
    to: BookingState.SUCCESS,
    trigger: 'BOOKING_CONFIRMED',
    sideEffects: () => sendConfirmationEmail()
  }
];
```

## Public Booking Interface

### Layout Variants
```typescript
enum BookingLayout {
  MONTH_VIEW = 'month_view',     // Calendar + time slots side by side
  WEEK_VIEW = 'week_view',       // Week calendar with time slots
  COLUMN_VIEW = 'column_view'    // Vertical layout for mobile
}

interface BookingLayoutProps {
  layout: BookingLayout;
  eventType: EventType;
  onLayoutChange: (layout: BookingLayout) => void;
  responsive: boolean;
}
```

### Step 1: Event Type Selection (Multi-Event Users)
```typescript
interface EventSelectionProps {
  eventTypes: PublicEventType[];
  onEventSelect: (eventType: PublicEventType) => void;
  userProfile: PublicUserProfile;
}

interface PublicEventType {
  slug: string;
  title: string;
  description?: string;
  duration: number;
  price?: number;
  currency?: string;
  locations: PublicLocation[];
  availability: AvailabilitySummary;
}

interface AvailabilitySummary {
  nextAvailableSlot?: Date;
  slotsThisWeek: number;
  slotsNextWeek: number;
}
```

**UI Components**:
- Event type cards with key information
- Duration and pricing display
- Next available slot indicator
- Location type icons
- Responsive grid layout

### Step 2: Date Selection
```typescript
interface DateSelectionProps {
  eventType: EventType;
  selectedDate?: string;
  onDateSelect: (date: string) => void;
  availability: DateAvailability[];
  timezone: string;
  layout: BookingLayout;
}

interface DateAvailability {
  date: string;
  available: boolean;
  slotsCount: number;
  isToday: boolean;
  isPast: boolean;
}
```

**UI Features**:
- Calendar with availability indicators
- Month/week navigation
- Timezone display and selection
- Available slot count per day
- Keyboard navigation support
- Mobile-optimized touch interactions

### Step 3: Time Selection
```typescript
interface TimeSelectionProps {
  eventType: EventType;
  selectedDate: string;
  selectedTime?: string;
  onTimeSelect: (time: string) => void;
  timeSlots: TimeSlot[];
  timezone: string;
}

interface TimeSlot {
  time: string;
  available: boolean;
  attendees?: number;
  maxAttendees?: number;
  price?: number;
  duration?: number;
}
```

**UI Features**:
- Time slot grid with clear availability
- Pricing information per slot
- Attendee count for group events
- Duration options (if multiple durations enabled)
- Scroll to current time
- Loading states for real-time availability

### Step 4: Form Completion
```typescript
interface BookingFormProps {
  eventType: EventType;
  selectedDateTime: {
    date: string;
    time: string;
    timezone: string;
  };
  onSubmit: (data: BookingFormData) => void;
  onBack: () => void;
  initialData?: Partial<BookingFormData>;
}

interface BookingFormData {
  // Required fields
  name: string;
  email: string;
  
  // Optional standard fields
  phone?: string;
  notes?: string;
  
  // Guest management
  guests?: GuestInfo[];
  
  // Custom fields (dynamic based on event type)
  customFields: Record<string, any>;
  
  // System fields
  timezone: string;
  locale: string;
  utmSource?: string;
  utmMedium?: string;
  utmCampaign?: string;
}

interface GuestInfo {
  name: string;
  email: string;
}
```

**Form Features**:
- Dynamic field rendering based on event type configuration
- Real-time validation with helpful error messages
- Guest invitation system (if enabled)
- Auto-save to prevent data loss
- Accessibility compliance (ARIA labels, keyboard navigation)
- Mobile-optimized input types

## Form Handling & Validation

### Client-Side Validation
```typescript
interface ValidationRule {
  field: string;
  type: 'required' | 'email' | 'phone' | 'minLength' | 'maxLength' | 'pattern';
  value?: any;
  message: string;
}

interface ValidationResult {
  valid: boolean;
  errors: FieldError[];
}

interface FieldError {
  field: string;
  message: string;
  type: string;
}

const VALIDATION_RULES: ValidationRule[] = [
  {
    field: 'name',
    type: 'required',
    message: 'Name is required'
  },
  {
    field: 'name',
    type: 'minLength',
    value: 2,
    message: 'Name must be at least 2 characters'
  },
  {
    field: 'email',
    type: 'required',
    message: 'Email is required'
  },
  {
    field: 'email',
    type: 'email',
    message: 'Please enter a valid email address'
  },
  {
    field: 'phone',
    type: 'phone',
    message: 'Please enter a valid phone number'
  }
];
```

### Real-Time Validation
```typescript
function useFormValidation(formData: BookingFormData, eventType: EventType) {
  const [errors, setErrors] = useState<FieldError[]>([]);
  const [touched, setTouched] = useState<Set<string>>(new Set());
  
  const validateField = useCallback((field: string, value: any) => {
    const fieldRules = getValidationRules(field, eventType);
    const fieldErrors = fieldRules
      .map(rule => validateRule(rule, value))
      .filter(Boolean);
    
    setErrors(prev => [
      ...prev.filter(error => error.field !== field),
      ...fieldErrors
    ]);
  }, [eventType]);
  
  const validateForm = useCallback(() => {
    const allErrors: FieldError[] = [];
    
    Object.entries(formData).forEach(([field, value]) => {
      const fieldRules = getValidationRules(field, eventType);
      const fieldErrors = fieldRules
        .map(rule => validateRule(rule, value))
        .filter(Boolean);
      allErrors.push(...fieldErrors);
    });
    
    setErrors(allErrors);
    return allErrors.length === 0;
  }, [formData, eventType]);
  
  return {
    errors,
    touched,
    validateField,
    validateForm,
    isValid: errors.length === 0
  };
}
```

### Form Submission Flow
```typescript
async function handleBookingSubmission(
  formData: BookingFormData,
  eventType: EventType,
  selectedDateTime: SelectedDateTime
): Promise<BookingResult> {
  try {
    // 1. Final validation
    const validationResult = validateBookingForm(formData, eventType);
    if (!validationResult.valid) {
      throw new ValidationError(validationResult.errors);
    }
    
    // 2. Check time slot availability (race condition protection)
    const isStillAvailable = await checkTimeSlotAvailability(
      eventType.id,
      selectedDateTime
    );
    
    if (!isStillAvailable) {
      throw new AvailabilityError('Time slot is no longer available');
    }
    
    // 3. Create booking
    const booking = await createBooking({
      eventTypeId: eventType.id,
      startTime: selectedDateTime.start,
      endTime: selectedDateTime.end,
      attendeeInfo: formData,
      timezone: selectedDateTime.timezone
    });
    
    // 4. Send confirmations
    await sendBookingConfirmations(booking);
    
    return {
      success: true,
      booking,
      confirmationNumber: booking.uid
    };
    
  } catch (error) {
    return {
      success: false,
      error: error.message,
      type: error.constructor.name
    };
  }
}
```

## Confirmation & Follow-up

### Booking Confirmation Page
```typescript
interface BookingConfirmationProps {
  booking: Booking;
  eventType: EventType;
  attendee: AttendeeInfo;
  onAddToCalendar: (provider: CalendarProvider) => void;
  onReschedule: () => void;
  onCancel: () => void;
}

interface BookingConfirmationData {
  confirmationNumber: string;
  eventDetails: {
    title: string;
    date: string;
    time: string;
    duration: number;
    timezone: string;
    location: LocationInfo;
  };
  attendeeInfo: AttendeeInfo;
  hostInfo: HostInfo;
  nextSteps: string[];
  calendarLinks: CalendarLink[];
  rescheduleLink: string;
  cancelLink: string;
}
```

**Confirmation Features**:
- Booking summary with all details
- Add to calendar buttons (Google, Outlook, Apple, ICS)
- Location information and directions
- Host contact information
- Reschedule/cancel links
- Next steps or preparation instructions
- Social sharing options

### Email Confirmations
```typescript
interface EmailConfirmationData {
  to: string;
  subject: string;
  template: 'booking_confirmation' | 'booking_reminder' | 'booking_cancelled';
  data: {
    attendeeName: string;
    eventTitle: string;
    dateTime: string;
    location: string;
    hostName: string;
    confirmationNumber: string;
    rescheduleLink: string;
    cancelLink: string;
    calendarInvite: string; // ICS attachment
  };
}
```

### Calendar Integration
```typescript
interface CalendarLink {
  provider: CalendarProvider;
  url: string;
  label: string;
}

enum CalendarProvider {
  GOOGLE = 'google',
  OUTLOOK = 'outlook',
  APPLE = 'apple',
  YAHOO = 'yahoo',
  ICS = 'ics'
}

function generateCalendarLinks(booking: Booking): CalendarLink[] {
  const baseParams = {
    title: booking.eventType.title,
    start: booking.startTime,
    end: booking.endTime,
    description: booking.description,
    location: booking.location
  };
  
  return [
    {
      provider: CalendarProvider.GOOGLE,
      url: generateGoogleCalendarUrl(baseParams),
      label: 'Add to Google Calendar'
    },
    {
      provider: CalendarProvider.OUTLOOK,
      url: generateOutlookCalendarUrl(baseParams),
      label: 'Add to Outlook'
    },
    {
      provider: CalendarProvider.ICS,
      url: generateICSDownloadUrl(baseParams),
      label: 'Download ICS file'
    }
  ];
}
```

## Error Handling & Recovery

### Error Types
```typescript
enum BookingErrorType {
  VALIDATION_ERROR = 'validation_error',
  AVAILABILITY_ERROR = 'availability_error',
  NETWORK_ERROR = 'network_error',
  SERVER_ERROR = 'server_error',
  PAYMENT_ERROR = 'payment_error',
  RATE_LIMIT_ERROR = 'rate_limit_error'
}

interface BookingError {
  type: BookingErrorType;
  message: string;
  field?: string;
  code?: string;
  recoverable: boolean;
  retryAfter?: number;
}
```

### Error Recovery Strategies
```typescript
interface ErrorRecoveryStrategy {
  errorType: BookingErrorType;
  strategy: 'retry' | 'fallback' | 'redirect' | 'manual';
  maxRetries?: number;
  retryDelay?: number;
  fallbackAction?: () => void;
}

const ERROR_RECOVERY_STRATEGIES: ErrorRecoveryStrategy[] = [
  {
    errorType: BookingErrorType.NETWORK_ERROR,
    strategy: 'retry',
    maxRetries: 3,
    retryDelay: 1000
  },
  {
    errorType: BookingErrorType.AVAILABILITY_ERROR,
    strategy: 'fallback',
    fallbackAction: () => redirectToTimeSelection()
  },
  {
    errorType: BookingErrorType.VALIDATION_ERROR,
    strategy: 'manual'
  },
  {
    errorType: BookingErrorType.RATE_LIMIT_ERROR,
    strategy: 'retry',
    maxRetries: 1,
    retryDelay: 5000
  }
];
```

### User-Friendly Error Messages
```typescript
const ERROR_MESSAGES = {
  [BookingErrorType.AVAILABILITY_ERROR]: {
    title: 'Time slot no longer available',
    message: 'Someone else just booked this time. Please select another slot.',
    action: 'Choose different time'
  },
  [BookingErrorType.VALIDATION_ERROR]: {
    title: 'Please check your information',
    message: 'Some required fields are missing or invalid.',
    action: 'Review form'
  },
  [BookingErrorType.NETWORK_ERROR]: {
    title: 'Connection problem',
    message: 'Please check your internet connection and try again.',
    action: 'Retry'
  },
  [BookingErrorType.SERVER_ERROR]: {
    title: 'Something went wrong',
    message: 'We\'re experiencing technical difficulties. Please try again in a few minutes.',
    action: 'Try again'
  }
};
```

This comprehensive booking flow ensures a smooth, accessible, and error-resilient experience for all users while maintaining data integrity and providing clear feedback throughout the process.
