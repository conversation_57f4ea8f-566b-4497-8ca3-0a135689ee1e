# Component Architecture Design

## Overview

This document defines the detailed component architecture for the CRM appointment booking module, specifying component hierarchies, interfaces, responsibilities, and integration patterns.

## Component Hierarchy

### Top-Level Architecture
```
AppointmentModule
├── AppointmentProvider (Context + State Management)
├── AppointmentRouting (Route-level components)
│   ├── BookingPublicPage (Public booking interface)
│   ├── AppointmentManagement (Admin interface)
│   └── CalendarView (Full calendar interface)
└── SharedComponents (Reusable across module)
```

### Booking Flow Components
```
BookingFlowWidget
├── BookingFlowProvider (State management)
├── BookingProgress (Progress indicator)
├── EventTypeSelection
│   ├── EventTypeCard
│   ├── EventTypeList
│   └── EventTypeFilter
├── DateTimeSelection
│   ├── BookingCalendar
│   │   ├── CalendarHeader
│   │   ├── CalendarGrid
│   │   └── CalendarNavigation
│   ├── TimeSlotGrid
│   │   ├── TimeSlotButton
│   │   ├── TimeSlotSkeleton
│   │   └── TimezoneSelector
│   └── AvailabilityIndicator
├── BookingForm
│   ├── AttendeeInformation
│   ├── DynamicBookingFields
│   ├── BookingNotes
│   └── BookingFormActions
├── BookingConfirmation
│   ├── BookingSummary
│   ├── ContactInformation
│   └── ConfirmationActions
└── BookingSuccess
    ├── BookingDetails
    ├── CalendarIntegration
    └── NextStepsActions
```

## Core Component Specifications

### 1. BookingFlowWidget (Root Component)

**Purpose**: Main container for the entire booking flow

**Interface**:
```typescript
interface BookingFlowWidgetProps {
  // Configuration
  config: BookingConfig;
  eventTypes?: EventType[];
  
  // Prefilled data
  initialData?: Partial<BookingFormData>;
  selectedEventType?: EventType;
  
  // CRM Integration
  contactId?: string;
  crmUserId?: string;
  
  // Styling
  theme?: BookingTheme;
  className?: string;
  
  // Callbacks
  onBookingComplete?: (booking: Booking) => void;
  onStepChange?: (step: BookingStep) => void;
  onError?: (error: BookingError) => void;
  
  // Feature flags
  features?: BookingFeatures;
}

interface BookingConfig {
  // Behavior
  autoAdvanceSteps: boolean;
  allowBackNavigation: boolean;
  enableProgressBar: boolean;
  
  // Validation
  requireEmailVerification: boolean;
  enableRealTimeValidation: boolean;
  
  // Integration
  enableCRMSync: boolean;
  enableCalendarSync: boolean;
  
  // UX
  showAvailabilityCount: boolean;
  enableTimezoneDetection: boolean;
  enableMobileOptimization: boolean;
}
```

**Responsibilities**:
- Initialize booking flow state
- Manage step progression
- Handle error boundaries
- Coordinate with CRM integration
- Provide theme context

**Usage Example**:
```typescript
<BookingFlowWidget
  config={bookingConfig}
  contactId="contact-123"
  onBookingComplete={(booking) => {
    // Create CRM task
    createFollowUpTask(booking);
    // Navigate to success page
    router.push('/appointments/success');
  }}
  theme={{
    primaryColor: '#2563eb',
    borderRadius: '8px',
    fontFamily: 'Inter, sans-serif'
  }}
/>
```

### 2. BookingCalendar Component

**Purpose**: Interactive calendar for date selection with availability display

**Interface**:
```typescript
interface BookingCalendarProps {
  // Data
  eventType: EventType;
  availability: AvailabilityData;
  
  // Selection
  selectedDate?: string;
  onDateSelect: (date: string) => void;
  
  // Navigation
  currentMonth: Date;
  onMonthChange: (month: Date) => void;
  
  // Display options
  showWeekNumbers?: boolean;
  highlightToday?: boolean;
  showUnavailableDates?: boolean;
  
  // Accessibility
  ariaLabel?: string;
  
  // Styling
  className?: string;
  customStyles?: CalendarStyles;
  
  // State
  isLoading?: boolean;
  error?: string;
}

interface AvailabilityData {
  [date: string]: {
    isAvailable: boolean;
    slotsCount?: number;
    hasConflicts?: boolean;
  };
}

interface CalendarStyles {
  container?: string;
  header?: string;
  navigation?: string;
  grid?: string;
  day?: string;
  selectedDay?: string;
  availableDay?: string;
  unavailableDay?: string;
  today?: string;
}
```

**Key Features**:
- Visual availability indicators
- Keyboard navigation support
- Mobile touch optimization
- Accessibility compliance
- Custom styling support

**Implementation Details**:
```typescript
const BookingCalendar = ({ 
  eventType, 
  availability, 
  selectedDate,
  onDateSelect,
  ...props 
}: BookingCalendarProps) => {
  const [focusedDate, setFocusedDate] = useState<Date>(new Date());
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  
  // Keyboard navigation
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    switch (event.key) {
      case 'ArrowLeft':
        navigateDate(-1);
        break;
      case 'ArrowRight':
        navigateDate(1);
        break;
      case 'ArrowUp':
        navigateDate(-7);
        break;
      case 'ArrowDown':
        navigateDate(7);
        break;
      case 'Enter':
      case ' ':
        if (isDateAvailable(focusedDate)) {
          onDateSelect(format(focusedDate, 'yyyy-MM-dd'));
        }
        break;
    }
  }, [focusedDate, onDateSelect]);
  
  // Render calendar grid
  const renderCalendarGrid = () => {
    const days = generateCalendarDays(currentMonth);
    
    return (
      <div className="grid grid-cols-7 gap-1">
        {days.map((day) => (
          <CalendarDay
            key={day.date}
            date={day.date}
            isSelected={day.date === selectedDate}
            isAvailable={availability[day.date]?.isAvailable}
            isToday={day.isToday}
            slotsCount={availability[day.date]?.slotsCount}
            onClick={() => onDateSelect(day.date)}
            onFocus={() => setFocusedDate(new Date(day.date))}
          />
        ))}
      </div>
    );
  };
  
  return (
    <div 
      className={cn("booking-calendar", props.className)}
      onKeyDown={handleKeyDown}
      role="grid"
      aria-label={props.ariaLabel || "Select appointment date"}
    >
      <CalendarHeader
        currentMonth={currentMonth}
        onMonthChange={setCurrentMonth}
      />
      {renderCalendarGrid()}
    </div>
  );
};
```

### 3. TimeSlotGrid Component

**Purpose**: Display and manage time slot selection

**Interface**:
```typescript
interface TimeSlotGridProps {
  // Data
  eventType: EventType;
  selectedDate: string;
  timeSlots: TimeSlot[];
  
  // Selection
  selectedTime?: string;
  onTimeSelect: (time: string) => void;
  
  // Display
  timezone: string;
  timeFormat: '12h' | '24h';
  groupByHour?: boolean;
  showDuration?: boolean;
  
  // Features
  enableMultiSelect?: boolean;
  maxSelections?: number;
  
  // State
  isLoading?: boolean;
  error?: string;
  
  // Styling
  className?: string;
  slotClassName?: string;
  
  // Accessibility
  ariaLabel?: string;
}

interface TimeSlot {
  time: string;           // ISO datetime string
  isAvailable: boolean;
  isReserved?: boolean;
  attendeeCount?: number;
  maxAttendees?: number;
  price?: number;
  
  // Additional metadata
  hostName?: string;
  location?: string;
  bufferTime?: {
    before: number;
    after: number;
  };
}
```

**Key Features**:
- Real-time availability updates
- Capacity management for group events
- Timezone conversion
- Loading states and error handling
- Keyboard navigation

**Implementation Example**:
```typescript
const TimeSlotGrid = ({
  timeSlots,
  selectedTime,
  onTimeSelect,
  timezone,
  timeFormat,
  ...props
}: TimeSlotGridProps) => {
  const [focusedSlot, setFocusedSlot] = useState<string | null>(null);
  
  // Group slots by hour if requested
  const groupedSlots = useMemo(() => {
    if (!props.groupByHour) return { ungrouped: timeSlots };
    
    return timeSlots.reduce((groups, slot) => {
      const hour = format(new Date(slot.time), 'HH');
      if (!groups[hour]) groups[hour] = [];
      groups[hour].push(slot);
      return groups;
    }, {} as Record<string, TimeSlot[]>);
  }, [timeSlots, props.groupByHour]);
  
  // Handle slot selection with validation
  const handleSlotSelect = (slot: TimeSlot) => {
    if (!slot.isAvailable || slot.isReserved) return;
    
    onTimeSelect(slot.time);
  };
  
  // Render individual time slot
  const renderTimeSlot = (slot: TimeSlot) => {
    const isSelected = slot.time === selectedTime;
    const isFocused = slot.time === focusedSlot;
    
    return (
      <TimeSlotButton
        key={slot.time}
        slot={slot}
        isSelected={isSelected}
        isFocused={isFocused}
        timeFormat={timeFormat}
        timezone={timezone}
        onClick={() => handleSlotSelect(slot)}
        onFocus={() => setFocusedSlot(slot.time)}
        showDuration={props.showDuration}
        className={props.slotClassName}
      />
    );
  };
  
  if (props.isLoading) {
    return <TimeSlotSkeleton />;
  }
  
  if (props.error) {
    return (
      <div className="time-slot-error">
        <p>Unable to load available times</p>
        <button onClick={() => window.location.reload()}>
          Try Again
        </button>
      </div>
    );
  }
  
  return (
    <div 
      className={cn("time-slot-grid", props.className)}
      role="grid"
      aria-label={props.ariaLabel || "Select appointment time"}
    >
      {Object.entries(groupedSlots).map(([hour, slots]) => (
        <div key={hour} className="time-slot-group">
          {props.groupByHour && (
            <h3 className="time-slot-group-header">
              {format(new Date(`2000-01-01T${hour}:00:00`), timeFormat === '12h' ? 'ha' : 'HH:mm')}
            </h3>
          )}
          <div className="time-slot-list">
            {slots.map(renderTimeSlot)}
          </div>
        </div>
      ))}
    </div>
  );
};
```

### 4. BookingForm Component

**Purpose**: Collect attendee information and booking details

**Interface**:
```typescript
interface BookingFormProps {
  // Configuration
  eventType: EventType;
  bookingFields: BookingField[];
  
  // Prefilled data
  initialData?: Partial<BookingFormData>;
  
  // Validation
  validationSchema?: ValidationSchema;
  enableRealTimeValidation?: boolean;
  
  // Submission
  onSubmit: (data: BookingFormData) => Promise<void>;
  onValidationChange?: (isValid: boolean) => void;
  
  // State
  isSubmitting?: boolean;
  
  // Styling
  className?: string;
  fieldClassName?: string;
  
  // Features
  enableAutoSave?: boolean;
  showRequiredIndicators?: boolean;
}

interface BookingFormData {
  // Required fields
  attendeeName: string;
  attendeeEmail: string;
  
  // Optional standard fields
  attendeePhone?: string;
  attendeeCompany?: string;
  attendeeTitle?: string;
  
  // Dynamic fields
  customFields: Record<string, any>;
  
  // System fields
  timezone: string;
  language: string;
  notes?: string;
  
  // CRM integration
  contactId?: string;
  source?: string;
}

interface BookingField {
  id: string;
  name: string;
  type: BookingFieldType;
  label: string;
  placeholder?: string;
  required: boolean;
  order: number;
  
  // Validation
  validation?: FieldValidation;
  
  // Options (for select/radio/checkbox)
  options?: FieldOption[];
  
  // Conditional display
  visibilityCondition?: VisibilityCondition;
  
  // Styling
  className?: string;
  helpText?: string;
}

enum BookingFieldType {
  TEXT = 'text',
  EMAIL = 'email',
  PHONE = 'phone',
  TEXTAREA = 'textarea',
  SELECT = 'select',
  MULTI_SELECT = 'multi_select',
  RADIO = 'radio',
  CHECKBOX = 'checkbox',
  NUMBER = 'number',
  URL = 'url',
  DATE = 'date',
  FILE = 'file'
}
```

**Key Features**:
- Dynamic field rendering
- Real-time validation
- Auto-save functionality
- Accessibility compliance
- CRM data prefilling

### 5. EventTypeCard Component

**Purpose**: Display event type information in a card format

**Interface**:
```typescript
interface EventTypeCardProps {
  eventType: EventType;
  
  // Selection
  isSelected?: boolean;
  onSelect?: (eventType: EventType) => void;
  
  // Display options
  showDescription?: boolean;
  showDuration?: boolean;
  showPrice?: boolean;
  showAvailability?: boolean;
  
  // Actions
  actions?: React.ReactNode;
  
  // Styling
  variant?: 'default' | 'compact' | 'detailed';
  className?: string;
  
  // State
  isLoading?: boolean;
  disabled?: boolean;
}
```

**Features**:
- Multiple display variants
- Action button integration
- Loading and disabled states
- Responsive design

## State Management Integration

### Component-Store Integration Pattern

```typescript
// Hook for component-store integration
const useBookingFlow = () => {
  // Store selectors
  const currentStep = useBookingStore(state => state.currentStep);
  const selectedEventType = useBookingStore(state => state.selectedEventType);
  const selectedDateTime = useBookingStore(state => state.selectedDateTime);
  const formData = useBookingStore(state => state.formData);
  
  // Store actions
  const { 
    setCurrentStep,
    selectEventType,
    selectDateTime,
    updateFormData,
    createBooking
  } = useBookingStore();
  
  // Computed values
  const canProgress = useMemo(() => {
    switch (currentStep) {
      case 'selecting_event_type':
        return !!selectedEventType;
      case 'selecting_date':
        return !!selectedDateTime.date;
      case 'selecting_time':
        return !!selectedDateTime.time;
      case 'filling_details':
        return isFormValid(formData);
      default:
        return false;
    }
  }, [currentStep, selectedEventType, selectedDateTime, formData]);
  
  // Navigation helpers
  const goToNextStep = useCallback(() => {
    if (!canProgress) return;
    
    const nextStep = getNextStep(currentStep);
    setCurrentStep(nextStep);
  }, [currentStep, canProgress]);
  
  const goToPreviousStep = useCallback(() => {
    const previousStep = getPreviousStep(currentStep);
    setCurrentStep(previousStep);
  }, [currentStep]);
  
  return {
    // State
    currentStep,
    selectedEventType,
    selectedDateTime,
    formData,
    canProgress,
    
    // Actions
    selectEventType,
    selectDateTime,
    updateFormData,
    createBooking,
    goToNextStep,
    goToPreviousStep
  };
};
```

## Error Handling Architecture

### Component Error Boundaries

```typescript
// Booking-specific error boundary
class BookingErrorBoundary extends Component<
  BookingErrorBoundaryProps,
  BookingErrorBoundaryState
> {
  constructor(props: BookingErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false, error: null };
  }
  
  static getDerivedStateFromError(error: Error): BookingErrorBoundaryState {
    return {
      hasError: true,
      error: {
        message: error.message,
        type: classifyBookingError(error),
        recoverable: isRecoverableError(error)
      }
    };
  }
  
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log to error tracking service
    logBookingError(error, errorInfo, {
      bookingStep: this.props.currentStep,
      eventTypeId: this.props.eventTypeId,
      userId: this.props.userId
    });
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <BookingErrorFallback
          error={this.state.error}
          onRetry={() => this.setState({ hasError: false, error: null })}
          onContactSupport={() => this.props.onContactSupport?.(this.state.error)}
        />
      );
    }
    
    return this.props.children;
  }
}

// Error classification for appropriate handling
const classifyBookingError = (error: Error): BookingErrorType => {
  if (error.message.includes('network')) return 'NETWORK_ERROR';
  if (error.message.includes('validation')) return 'VALIDATION_ERROR';
  if (error.message.includes('availability')) return 'AVAILABILITY_ERROR';
  if (error.message.includes('auth')) return 'AUTH_ERROR';
  return 'UNKNOWN_ERROR';
};
```

## Accessibility Implementation

### ARIA Implementation Strategy

```typescript
// Accessibility hook for consistent ARIA implementation
const useBookingAccessibility = (componentType: string) => {
  const announcements = useRef<string[]>([]);
  
  const announce = (message: string) => {
    announcements.current.push(message);
    // Trigger screen reader announcement
    const liveRegion = document.getElementById('booking-live-region');
    if (liveRegion) {
      liveRegion.textContent = message;
      setTimeout(() => {
        liveRegion.textContent = '';
      }, 1000);
    }
  };
  
  const getAriaProps = (element: string) => {
    const baseProps = {
      role: getElementRole(element),
      'aria-label': getElementLabel(componentType, element),
      'aria-describedby': getElementDescription(element)
    };
    
    return baseProps;
  };
  
  return {
    announce,
    getAriaProps,
    announcements: announcements.current
  };
};

// Usage in components
const BookingCalendar = (props: BookingCalendarProps) => {
  const { announce, getAriaProps } = useBookingAccessibility('calendar');
  
  const handleDateSelect = (date: string) => {
    props.onDateSelect(date);
    announce(`Selected ${formatDate(date, 'EEEE, MMMM do, yyyy')}`);
  };
  
  return (
    <div 
      {...getAriaProps('calendar')}
      onKeyDown={handleKeyboardNavigation}
    >
      {/* Calendar content */}
    </div>
  );
};
```

## Performance Optimization

### Component-Level Optimizations

```typescript
// Memoization strategy for expensive components
const BookingCalendar = memo(({ 
  availability, 
  selectedDate, 
  onDateSelect,
  ...props 
}: BookingCalendarProps) => {
  // Memoize expensive calculations
  const calendarDays = useMemo(() => 
    generateCalendarDays(props.currentMonth, availability),
    [props.currentMonth, availability]
  );
  
  // Stable callback references
  const handleDateSelect = useCallback(
    (date: string) => onDateSelect(date),
    [onDateSelect]
  );
  
  return (
    <CalendarGrid
      days={calendarDays}
      selectedDate={selectedDate}
      onDateSelect={handleDateSelect}
    />
  );
}, (prevProps, nextProps) => {
  // Custom comparison for optimization
  return (
    prevProps.selectedDate === nextProps.selectedDate &&
    prevProps.currentMonth === nextProps.currentMonth &&
    isEqual(prevProps.availability, nextProps.availability)
  );
});

// Virtualization for large lists
const EventTypeList = ({ eventTypes }: EventTypeListProps) => {
  const [containerRef, { height }] = useElementSize();
  
  return (
    <div ref={containerRef} className="event-type-list">
      <FixedSizeList
        height={height}
        itemCount={eventTypes.length}
        itemSize={120}
        itemData={eventTypes}
      >
        {EventTypeCard}
      </FixedSizeList>
    </div>
  );
};
```

## Testing Strategy

### Component Testing Patterns

```typescript
// Test utilities for booking components
export const renderBookingComponent = (
  component: React.ReactElement,
  options?: {
    initialStore?: Partial<BookingStore>;
    eventType?: EventType;
    availability?: AvailabilityData;
  }
) => {
  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <BookingProvider initialState={options?.initialStore}>
      <QueryClient>
        {children}
      </QueryClient>
    </BookingProvider>
  );
  
  return render(component, { wrapper: TestWrapper });
};

// Component-specific test helpers
export const selectDate = async (date: string) => {
  const dateButton = await screen.findByRole('button', { 
    name: new RegExp(date) 
  });
  fireEvent.click(dateButton);
};

export const selectTimeSlot = async (time: string) => {
  const timeButton = await screen.findByRole('button', { 
    name: new RegExp(time) 
  });
  fireEvent.click(timeButton);
};

export const fillBookingForm = async (data: Partial<BookingFormData>) => {
  if (data.attendeeName) {
    const nameInput = screen.getByLabelText(/name/i);
    fireEvent.change(nameInput, { target: { value: data.attendeeName } });
  }
  
  if (data.attendeeEmail) {
    const emailInput = screen.getByLabelText(/email/i);
    fireEvent.change(emailInput, { target: { value: data.attendeeEmail } });
  }
  
  // Handle other fields...
};

// Integration test example
describe('BookingFlow Integration', () => {
  it('should complete full booking flow', async () => {
    const onBookingComplete = jest.fn();
    
    renderBookingComponent(
      <BookingFlowWidget
        eventTypes={mockEventTypes}
        onBookingComplete={onBookingComplete}
      />
    );
    
    // Select event type
    fireEvent.click(screen.getByText('30 Min Meeting'));
    
    // Select date
    await selectDate('15');
    
    // Select time
    await selectTimeSlot('9:00 AM');
    
    // Fill form
    await fillBookingForm({
      attendeeName: 'John Doe',
      attendeeEmail: '<EMAIL>'
    });
    
    // Submit booking
    fireEvent.click(screen.getByText('Book Appointment'));
    
    // Verify completion
    await waitFor(() => {
      expect(onBookingComplete).toHaveBeenCalledWith(
        expect.objectContaining({
          attendeeName: 'John Doe',
          attendeeEmail: '<EMAIL>'
        })
      );
    });
  });
});
```

## Component Documentation

### Storybook Integration

```typescript
// Component stories for documentation
export default {
  title: 'Booking/BookingCalendar',
  component: BookingCalendar,
  parameters: {
    docs: {
      description: {
        component: 'Interactive calendar for selecting appointment dates'
      }
    }
  }
} as Meta<typeof BookingCalendar>;

export const Default: Story<BookingCalendarProps> = {
  args: {
    eventType: mockEventType,
    availability: mockAvailability,
    onDateSelect: action('date-selected')
  }
};

export const WithLimitedAvailability: Story<BookingCalendarProps> = {
  args: {
    ...Default.args,
    availability: limitedAvailability
  }
};

export const Loading: Story<BookingCalendarProps> = {
  args: {
    ...Default.args,
    isLoading: true
  }
};
```

## Next Steps

1. **Component Implementation**: Begin with core components (BookingCalendar, TimeSlotGrid)
2. **State Integration**: Connect components with Zustand stores
3. **Testing Setup**: Implement testing utilities and write component tests
4. **Documentation**: Create comprehensive Storybook stories
5. **Accessibility Audit**: Ensure all components meet accessibility standards

## Related Documents

- [01-crm-integration-design.md](./01-crm-integration-design.md) - Overall architecture
- [03-state-management-design.md](./03-state-management-design.md) - State management
- [04-ui-component-design.md](./04-ui-component-design.md) - UI specifications
- [../implementation/02-component-specifications.md](../implementation/02-component-specifications.md) - Implementation details
